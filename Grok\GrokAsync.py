# pip install httpx python-dotenv
import base64
import os
import httpx
from dotenv import load_dotenv
from typing import Dict, Any, Union, List
import json
import asyncio
import sys
from pathlib import Path
import random
import time
import traceback

parrent_dir = str(Path(__file__).resolve().parent.parent)
sys.path.append(parrent_dir)
from prompt import PROMPT_EXAMPLE_GEMINI

# Загрузка переменных окружения из файла .env
load_dotenv()

# Получаем API ключ из переменной окружения GROK_API_KEY
api_key = os.getenv("GROK_API_KEY")
if not api_key:
    print("❌ КРИТИЧЕСКАЯ ОШИБКА: Не найден API ключ GROK_API_KEY в файле .env")
    exit(1)

print(f"✅ API ключ загружен: {api_key[:8]}...{api_key[-4:]}")


# Глобальный rate limiter с более консервативными настройками
class RateLimiter:
    def __init__(self, max_rpm=300, burst_limit=3):  # Снижено с 450 до 300 RPM и с 5 до 3 burst
        """
        max_rpm: максимум запросов в минуту 
        burst_limit: максимум одновременных запросов
        """
        self.max_rpm = max_rpm
        self.burst_limit = burst_limit
        self.semaphore = asyncio.Semaphore(burst_limit)
        self.request_times = []
        self.lock = asyncio.Lock()
        
    async def acquire(self):
        """Ожидает возможности выполнить запрос с учетом rate limits"""
        async with self.semaphore:
            async with self.lock:
                now = time.time()
                # Удаляем запросы старше минуты
                self.request_times = [t for t in self.request_times if now - t < 60]
                
                # Если достигли лимита в минуту, ждем
                if len(self.request_times) >= self.max_rpm:
                    sleep_time = 60 - (now - self.request_times[0]) + random.uniform(0.5, 1.5)
                    if sleep_time > 0:
                        print(f"⏳ Rate limit достигнут ({len(self.request_times)}/{self.max_rpm}). Ожидание {sleep_time:.1f} сек...")
                        await asyncio.sleep(sleep_time)
                        now = time.time()
                        self.request_times = [t for t in self.request_times if now - t < 60]
                
                # Минимальный интервал между запросами увеличен
                if self.request_times:
                    min_interval = 60.0 / self.max_rpm * 1.5  # Увеличен запас до 50%
                    last_request = self.request_times[-1]
                    if now - last_request < min_interval:
                        wait_time = min_interval - (now - last_request)
                        await asyncio.sleep(wait_time)
                
                self.request_times.append(time.time())
                print(f"📊 Запросов за последнюю минуту: {len(self.request_times)}/{self.max_rpm}")


# Создаем глобальный rate limiter с более консервативными настройками
rate_limiter = RateLimiter(max_rpm=300, burst_limit=3)


def clear_text(json_string: Union[str, Dict[str, Any]]) -> Dict[str, Any]:
    """Очищает и парсит JSON строку"""
    if isinstance(json_string, str):
        try:
            json_string = json_string.strip()
            
            # Обрабатываем различные форматы JSON
            if json_string.startswith('```json'):
                json_string = json_string[7:]
            if json_string.endswith('```'):
                json_string = json_string[:-3]
            if json_string.startswith('"'):
                json_string = json_string[1:]
            if json_string.endswith('"'):
                json_string = json_string[:-1]
            
            # Преобразуем строку JSON в словарь Python
            extract_data = json.loads(json_string)
            return extract_data
        except json.JSONDecodeError as e:
            print(f"Не удалось распарсить JSON: {e}")
            print(f"Проблемная строка (первые 200 символов): {json_string[:200]}...")
            return {}
    return json_string if isinstance(json_string, dict) else {}


async def make_api_request_with_retry(
        client: httpx.AsyncClient,
        request_data: Dict[str, Any],
        headers: Dict[str, str],
        max_retries: int = 3,
        base_delay: float = 5.0  # Увеличена базовая задержка с 2 до 5
) -> Dict[str, Any]:
    """
    Выполняет API запрос с механизмом повторных попыток и детальной диагностикой
    """
    
    for attempt in range(max_retries + 1):
        try:
            # Ожидаем разрешение от rate limiter
            await rate_limiter.acquire()
            
            print(f"📡 Отправка запроса (попытка {attempt + 1}/{max_retries + 1})...")
            
            # Сокращаем размер контента если слишком большой
            content_size = len(json.dumps(request_data))
            if content_size > 50000:  # Если больше 50KB
                print(f"⚠️ Большой размер запроса: {content_size/1024:.1f} KB")
                # Сокращаем max_completion_tokens для больших запросов
                request_data["max_completion_tokens"] = min(request_data.get("max_completion_tokens", 5000), 5000)
            
            start_time = time.time()
            response = await client.post(
                "https://api.x.ai/v1/chat/completions",
                json=request_data,
                headers=headers
            )
            elapsed = time.time() - start_time
            
            print(f"📊 Статус ответа: {response.status_code} (время: {elapsed:.1f} сек)")
            
            if response.status_code == 429:  # Too Many Requests
                retry_after = response.headers.get('Retry-After', '60')
                wait_time = float(retry_after) + random.uniform(5, 10)
                print(f"⚠️ Rate limit (429). Headers: {dict(response.headers)}")
                print(f"⏳ Ожидание {wait_time:.1f} сек...")
                await asyncio.sleep(wait_time)
                continue
            
            if response.status_code == 401:
                print(f"❌ Ошибка авторизации (401): Проверьте API ключ")
                print(f"Детали: {response.text}")
                return {}
            
            if response.status_code == 400:
                print(f"❌ Неверный запрос (400)")
                print(f"Детали ошибки: {response.text[:500]}")
                return {}
                
            response.raise_for_status()
            
            # Проверяем структуру ответа
            try:
                response_json = response.json()
            except json.JSONDecodeError as e:
                print(f"❌ Ошибка парсинга JSON ответа: {e}")
                print(f"Сырой ответ: {response.text[:500]}")
                return {}
            
            # Проверяем наличие необходимых полей
            if "choices" not in response_json:
                print(f"❌ В ответе отсутствует поле 'choices'")
                print(f"Структура ответа: {list(response_json.keys())}")
                return {}
            
            if not response_json["choices"]:
                print(f"❌ Массив 'choices' пустой")
                return {}
            
            if "message" not in response_json["choices"][0]:
                print(f"❌ В ответе отсутствует поле 'message'")
                print(f"Структура choices[0]: {list(response_json['choices'][0].keys())}")
                return {}
            
            response_text = response_json["choices"][0]["message"]["content"]
            
            # Информация об использованных токенах
            if 'usage' in response_json:
                tokens = response_json['usage']
                print(f"📈 Токены: вход={tokens.get('prompt_tokens', 0)}, "
                      f"выход={tokens.get('completion_tokens', 0)}, "
                      f"всего={tokens.get('total_tokens', 0)}")
            
            clear_result = clear_text(response_text)
            
            print(f"✅ Запрос успешно выполнен с попытки {attempt + 1}")
            return clear_result
            
        except httpx.ConnectError as e:
            print(f"❌ Ошибка подключения на попытке {attempt + 1}: Не удается подключиться к API")
            print(f"Детали: {str(e)}")
            
        except httpx.TimeoutException as e:
            print(f"❌ Таймаут на попытке {attempt + 1}: Превышено время ожидания ответа")
            print(f"Детали: {str(e)}")
            # Для таймаутов используем увеличенную задержку
            if attempt < max_retries:
                wait_time = base_delay * (1.5 ** attempt) + random.uniform(2, 5)
                print(f"⏳ Ожидание {wait_time:.1f} сек перед следующей попыткой...")
                await asyncio.sleep(wait_time)
                continue
            
        except httpx.HTTPStatusError as e:
            print(f"❌ HTTP ошибка {e.response.status_code} на попытке {attempt + 1}")
            print(f"Детали: {e.response.text[:500] if e.response.text else 'Нет деталей'}")
            
            if e.response.status_code in [500, 502, 503, 504]:  # Server errors
                if attempt < max_retries:
                    wait_time = base_delay * (2 ** attempt) + random.uniform(3, 7)
                    print(f"⚠️ Ошибка сервера. Повтор через {wait_time:.1f} сек...")
                    await asyncio.sleep(wait_time)
                    continue
            
        except httpx.RequestError as e:
            print(f"❌ Ошибка запроса на попытке {attempt + 1}")
            print(f"Тип ошибки: {type(e).__name__}")
            print(f"Детали: {str(e)}")
            
        except Exception as e:
            print(f"❌ Неожиданная ошибка на попытке {attempt + 1}")
            print(f"Тип ошибки: {type(e).__name__}")
            print(f"Детали: {str(e)}")
            print(f"Трейсбек:")
            traceback.print_exc()
        
        # Если не последняя попытка, ждем перед следующей
        if attempt < max_retries:
            wait_time = base_delay * (1.5 ** attempt) + random.uniform(1, 3)
            print(f"⏳ Ожидание {wait_time:.1f} сек перед следующей попыткой...")
            await asyncio.sleep(wait_time)
        else:
            print(f"🚫 Все {max_retries + 1} попытки исчерпаны")
    
    return {}


async def extract_data_by_grok_async(
        content: str,
        prompt=PROMPT_EXAMPLE_GEMINI,
        max_retries: int = 3,
        base_delay: float = 5.0,
        timeout: float = 90.0  # УВЕЛИЧЕН ТАЙМАУТ с 30 до 90 секунд!
) -> Dict[str, Any]:
    """
    Извлекает данные через GROK API с механизмом повторных попыток
    
    Args:
        content: Контент для обработки
        prompt: Промпт для модели
        max_retries: Максимальное количество повторных попыток
        base_delay: Базовая задержка для экспоненциального роста
        timeout: Таймаут для HTTP клиента в секундах (увеличен до 90)
    """
    
    # Проверяем размер контента
    if len(content) > 100000:  # Если больше 100KB
        print(f"⚠️ Очень большой контент: {len(content)/1024:.1f} KB. Обрезаем...")
        content = content[:100000]
    
    # Подготавливаем сообщения для API
    messages = [
        {"role": "system", "content": prompt},
        {"role": "user", "content": content},
    ]
    
    # Подготавливаем данные для запроса с оптимизированными параметрами
    request_data = {
        "model": "grok-3-mini",  
        "reasoning_effort": "low",  # Снижено до "low" для максимальной скорости
        "messages": messages,
        "temperature": 0.1,
        "max_completion_tokens": 5000,  # Снижено с 8000 до 5000
        "top_p": 0.1,
        "stream": False,
        "response_format": {"type": "json_object"},
        "stop": None,
    }
    
    # Заголовки для запроса
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # Создаем клиент с увеличенным таймаутом
    timeout_config = httpx.Timeout(
        connect=10.0,    # Таймаут на подключение
        read=timeout,     # Таймаут на чтение (90 секунд)
        write=10.0,       # Таймаут на запись
        pool=10.0         # Таймаут на получение соединения из пула
    )
    
    # Создаем асинхронный HTTP-клиент и выполняем запрос с повторными попытками
    async with httpx.AsyncClient(timeout=timeout_config) as client:
        result = await make_api_request_with_retry(
            client=client,
            request_data=request_data,
            headers=headers,
            max_retries=max_retries,
            base_delay=base_delay
        )
        return result


async def process_multiple_contents(
        contents: List[str],
        prompt=PROMPT_EXAMPLE_GEMINI,
        max_retries: int = 3,
        base_delay: float = 5.0,
        max_concurrent: int = 2  # Снижено с 5 до 2
) -> List[Dict[str, Any]]:
    """
    Асинхронная обработка нескольких контентов параллельно с контролем параллельности
    """
    print(f"🚀 Начинаем параллельную обработку {len(contents)} контентов...")
    
    semaphore = asyncio.Semaphore(max_concurrent)
    
    async def limited_extract(content, index):
        async with semaphore:
            print(f"  Обработка контента {index + 1}/{len(contents)}")
            result = await extract_data_by_grok_async(content, prompt, max_retries, base_delay)
            # Добавляем задержку между запросами
            await asyncio.sleep(random.uniform(0.5, 1.5))
            return result
    
    tasks = [limited_extract(content, i) for i, content in enumerate(contents)]
    
    start_time = time.time()
    results = await asyncio.gather(*tasks, return_exceptions=True)
    end_time = time.time()
    
    print(f"⏱️ Общее время обработки: {end_time - start_time:.2f} секунд")
    
    # Обрабатываем результаты и исключения
    processed_results = []
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            print(f"❌ Критическая ошибка при обработке контента {i}: {result}")
            processed_results.append({})
        else:
            processed_results.append(result)
    
    return processed_results


async def process_multiple_contents_batch(
        contents: List[str],
        prompt=PROMPT_EXAMPLE_GEMINI,
        batch_size: int = 2,  # Снижено с 3 до 2
        delay_between_batches: float = 3.0  # Увеличено с 1 до 3 секунд
) -> List[Dict[str, Any]]:
    """
    Обрабатывает контенты батчами для избежания rate limiting
    """
    
    print(f"🚀 Обработка {len(contents)} контентов батчами по {batch_size}...")
    results = []
    
    for i in range(0, len(contents), batch_size):
        batch = contents[i:i + batch_size]
        batch_num = i // batch_size + 1
        total_batches = (len(contents) + batch_size - 1) // batch_size
        
        print(f"📦 Обработка батча {batch_num}/{total_batches}")
        
        tasks = [extract_data_by_grok_async(content, prompt) for content in batch]
        batch_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        for result in batch_results:
            if isinstance(result, Exception):
                print(f"❌ Ошибка в батче: {result}")
                results.append({})
            else:
                results.append(result)
        
        # Задержка между батчами
        if i + batch_size < len(contents):
            print(f"⏳ Пауза {delay_between_batches} сек между батчами...")
            await asyncio.sleep(delay_between_batches)
    
    return results


async def test_connection():
    """Тестирует подключение к Grok API"""
    print("\n🔍 Тестирование подключения к Grok API...")
    
    test_content = "Test"
    test_prompt = """Reply with JSON: {"status": "ok", "message": "API works"}"""
    
    try:
        result = await extract_data_by_grok_async(
            content=test_content,
            prompt=test_prompt,
            max_retries=1,
            timeout=30.0  # Меньший таймаут для теста
        )
        
        if result:
            print("✅ Подключение успешно установлено!")
            print(f"Тестовый ответ: {json.dumps(result, ensure_ascii=False, indent=2)}")
            return True
        else:
            print("❌ Подключение установлено, но ответ пустой")
            return False
            
    except Exception as e:
        print(f"❌ Не удалось подключиться к API")
        print(f"Ошибка: {e}")
        return False


if __name__ == "__main__":
    if os.name == "nt":
        os.system("cls")
    else:
        os.system("clear")
    
    # Тестируем подключение
    asyncio.run(test_connection())