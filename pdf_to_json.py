"""
Извлекает каждую страницу из PDF файла и сохраняет в отдельный PDF файл.
Извлекает текст из PDF файла используя GeminiAI и сохраняет его в JSON формате.
"""
import os
import json
import fitz  # pip install PyMuPDF
import tempfile
import atexit

from numpy import s_
from Gemini.GeminiAI import extract_entity_with_cache_async, process_multiple_files_async, clear_text
import asyncio
from prompt import PROMPT_OCR_CONTROL
from dotenv import load_dotenv
from ExtractEmptyFiles import is_blank_async
import pandas as pd


load_dotenv()

# количество одновременных запросов к GeminiAI
MAX_CONCURRENT_REQUESTS = 2

# Глобальный список для отслеживания временных файлов
_temp_files_created = set()

def register_temp_file(file_path: str):
    """Регистрирует временный файл для последующей очистки."""
    _temp_files_created.add(file_path)

def cleanup_temp_files():
    """Очищает все зарегистрированные временные файлы."""
    cleaned_count = 0
    failed_count = 0

    for file_path in _temp_files_created.copy():
        try:
            if os.path.exists(file_path):
                os.unlink(file_path)
                cleaned_count += 1
            _temp_files_created.remove(file_path)
        except Exception as e:
            print(f"Не удалось удалить временный файл {file_path}: {e}")
            failed_count += 1

    if cleaned_count > 0:
        print(f"Очищено {cleaned_count} временных файлов")
    if failed_count > 0:
        print(f"Не удалось очистить {failed_count} временных файлов")

    # Также очищаем временную директорию системы
    try:
        import tempfile
        temp_dir = tempfile.gettempdir()
        for filename in os.listdir(temp_dir):
            if filename.startswith('tmp') and filename.endswith('.pdf'):
                file_path = os.path.join(temp_dir, filename)
                try:
                    # Проверяем, не слишком ли старый файл (старше 1 часа)
                    import time
                    if time.time() - os.path.getctime(file_path) > 3600:
                        os.unlink(file_path)
                        print(f"Удален старый временный файл: {filename}")
                except:
                    pass
    except Exception as e:
        print(f"Ошибка при очистке системной временной директории: {e}")

# Регистрируем функцию очистки для автоматического вызова при завершении программы
atexit.register(cleanup_temp_files)

def create_json(file_name, page_number, text):
    """
    Создает структуру JSON данных для страницы.

    Args:
        file_name (str): Имя PDF файла
        page_number (int): Номер страницы (начиная с 1)
        text (str or None): Извлеченный текст со страницы

    Returns:
        dict: JSON данные с file_name, page_number и description
    """
    data = {
        "file_name": file_name,
        "page_number": page_number,
        "description": text
    }
    return data





async def process_pdf_file(input_path: str):
    """
    Обрабатывает PDF файл, извлекая текст с каждой страницы и сохраняя его в JSON файл.

    Args:
        input_path (str): Путь к входному PDF файлу

    Функция:
    - Проверяет существование входного файла
    - Обрабатывает каждую страницу, пропуская пустые страницы
    - Использует Gemini AI для извлечения текста с непустых страниц
    - Сохраняет все данные страниц в JSON файл в формате {"doc": [список страниц]}
    """
    # Проверяем существование входного файла
    if not os.path.exists(input_path):
        print(f"Входной файл не найден: {input_path}")
        return

    # Определяем путь вывода и параметры обнаружения пустых страниц
    output_path = input_path.replace(".pdf", ".json")
    threshold = 0.99  # 0.99 - 99% порог белого цвета для обнаружения пустых страниц
    white_level = 250  # 255 - белый, 0 - черный

    # Удаляем существующий файл вывода, если он есть
    if os.path.exists(output_path):
        os.remove(output_path)

    # Инициализируем DataFrame для хранения данных страниц
    df = pd.DataFrame(columns=["file_name", "page_number", "description"])

    # Открываем PDF документ
    with fitz.open(input_path) as doc:
        total_pages = len(doc)

        # Обрабатываем каждую страницу
        for page_number in range(total_pages):
            page = doc.load_page(page_number)
            # Инициализируем с None текстом (для пустых страниц)
            json_data = create_json(os.path.basename(input_path), page_number + 1, None)

            # Проверяем, не является ли страница пустой
            if not await is_blank_async(page, threshold, white_level):
                # Создаем временный PDF для одной страницы
                temp_pdf = tempfile.NamedTemporaryFile(suffix='.pdf', delete=False)
                temp_pdf_path = temp_pdf.name
                temp_pdf.close()

                # Регистрируем файл для очистки
                register_temp_file(temp_pdf_path)

                try:
                    temp_doc = fitz.open()
                    temp_doc.insert_pdf(doc, from_page=page_number, to_page=page_number)
                    temp_doc.save(temp_pdf_path)
                    temp_doc.close()

                    # Извлекаем текст с помощью Gemini AI
                    result = await extract_entity_with_cache_async(temp_pdf_path, PROMPT_OCR_CONTROL)
                    result = clear_text(result)
                    json_data = create_json(os.path.basename(input_path), page_number + 1, result)
                finally:
                    # Гарантированная очистка временного файла
                    try:
                        if os.path.exists(temp_pdf_path):
                            os.unlink(temp_pdf_path)
                        if temp_pdf_path in _temp_files_created:
                            _temp_files_created.remove(temp_pdf_path)
                    except:
                        pass

            # Добавляем данные страницы в DataFrame
            df = pd.concat([df, pd.DataFrame([json_data])], ignore_index=True)
            print(f"{os.path.basename(input_path)}; Обработана страница {page_number + 1}/{total_pages}")

        # Если данные не были извлечены (сбой), заполняем все страницы None
        if df.empty:
            for page_number in range(total_pages):
                json_data = create_json(os.path.basename(input_path), page_number + 1, None)
                df = pd.concat([df, pd.DataFrame([json_data])], ignore_index=True)

    # Сохраняем DataFrame в JSON файл
    data = {"doc": df.to_dict(orient="records")}
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)

    # Финальная очистка временных файлов
    cleanup_temp_files()
    

async def process_pdf_file_batch(input_path: str):
    """
    Обрабатывает PDF файл, извлекая текст с каждой страницы используя пакетную обработку.
    
    Args:
        input_path (str): Путь к входному PDF файлу
    """
    if not os.path.exists(input_path):
        print(f"Входной файл не найден: {input_path}")
        return

    output_path = input_path.replace(".pdf", ".json")
    threshold = 0.99
    white_level = 250

    if os.path.exists(output_path):
        os.remove(output_path)

    df = pd.DataFrame(columns=["file_name", "page_number", "description"])

    # Создаем временные файлы для всех страниц
    temp_files = []
    page_numbers = []
    
    with fitz.open(input_path) as doc:
        total_pages = len(doc)
        
        for page_number in range(total_pages):
            page = doc.load_page(page_number)
            
            # Проверяем, не пустая ли страница
            if not await is_blank_async(page, threshold, white_level):
                # Создаем временный файл для непустой страницы
                temp_pdf = tempfile.NamedTemporaryFile(suffix='.pdf', delete=False)
                temp_pdf_path = temp_pdf.name
                temp_pdf.close()

                # Регистрируем файл для очистки
                register_temp_file(temp_pdf_path)

                try:
                    temp_doc = fitz.open()
                    temp_doc.insert_pdf(doc, from_page=page_number, to_page=page_number)
                    temp_doc.save(temp_pdf_path)
                    temp_doc.close()

                    temp_files.append(temp_pdf_path)
                    page_numbers.append(page_number + 1)
                except Exception as e:
                    # Если создание файла не удалось, удаляем его из списка
                    try:
                        if os.path.exists(temp_pdf_path):
                            os.unlink(temp_pdf_path)
                        if temp_pdf_path in _temp_files_created:
                            _temp_files_created.remove(temp_pdf_path)
                    except:
                        pass
                    print(f"Ошибка при создании временного файла для страницы {page_number + 1}: {e}")
                    continue
            else:
                # Для пустых страниц добавляем None сразу
                json_data = create_json(os.path.basename(input_path), page_number + 1, None)
                df = pd.concat([df, pd.DataFrame([json_data])], ignore_index=True)
                print(f"{os.path.basename(input_path)}; Пропущена пустая страница {page_number + 1}/{total_pages}")

    # Пакетная обработка непустых страниц
    if temp_files:
        print(f"Начинаем пакетную обработку {len(temp_files)} непустых страниц...")
        
        batch_results = await process_multiple_files_async(
            file_paths=temp_files,
            prompt=PROMPT_OCR_CONTROL,
            max_concurrent=MAX_CONCURRENT_REQUESTS
        )
        
        # Обрабатываем результаты пакетной обработки
        for i, temp_file in enumerate(temp_files):
            page_num = page_numbers[i]
            result = batch_results.get(temp_file)
            result = clear_text(result)
            
            json_data = create_json(os.path.basename(input_path), page_num, result)
            df = pd.concat([df, pd.DataFrame([json_data])], ignore_index=True)
            print(f"{os.path.basename(input_path)}; Обработана страница {page_num}/{total_pages}")
            
            # Удаляем временный файл
            try:
                os.unlink(temp_file)
            except:
                pass

    # Сохраняем результаты
    data = {"doc": df.to_dict(orient="records")}
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)

    print(f"Завершена обработка файла: {os.path.basename(input_path)}")

    # Финальная очистка временных файлов
    cleanup_temp_files()
            
            
# Основной блок выполнения
if __name__ == "__main__":
    
    # # Вариант 1. Обработка одного файла.
    # # Путь к PDF файлу для обработки
    # pdf_path = r"c:\Scan\All\AlreadyAddToDb\2025-09-02_142257.pdf"
    # # Запускаем асинхронную функцию обработки PDF
    # asyncio.run(process_pdf_file(pdf_path))


    # Вариант 2. Обработка одного файла.
    # обработка всех pdf в папке
    folder_path = r"c:\Scan\All\AlreadyAddToDb\t"
    for filename in os.listdir(folder_path):
        if filename.endswith(".pdf"):
            pdf_path = os.path.join(folder_path, filename)
            # asyncio.run(process_pdf_file(pdf_path))
            asyncio.run(process_pdf_file_batch(pdf_path))

    # Финальная очистка всех временных файлов после завершения обработки
    print("\n=== ЗАВЕРШЕНИЕ ОБРАБОТКИ ===")
    cleanup_temp_files()
    print("Все временные файлы очищены.")
