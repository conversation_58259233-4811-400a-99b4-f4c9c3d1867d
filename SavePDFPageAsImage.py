# pip install google-cloud-billing

# ==============================================================================
# СИСТЕМА ОБРАБОТКИ ДОКУМЕНТОВ С ИСКУССТВЕННЫМ ИНТЕЛЛЕКТОМ
# ScanDocument - Асинхронная обработка PDF документов с OCR и AI анализом
# ==============================================================================

"""
АРХИТЕКТУРА СИСТЕМЫ ОБРАБОТКИ ДОКУМЕНТОВ

Эта система предоставляет комплексное решение для асинхронной обработки PDF документов
с использованием современных AI технологий для OCR и анализа содержимого.

ОСНОВНЫЕ КОМПОНЕНТЫ:
1. SmartRateLimiter - Продвинутый rate limiter с sliding window
2. AI Model Switching - Автоматическое переключение между моделями Gemini
3. PostgreSQL Caching - Единая система кэширования для всех AI сервисов
4. Batch Processing - Пакетная обработка с контролем параллельности
5. Error Recovery - Устойчивость к ошибкам с повторными попытками

КЛЮЧЕВЫЕ ВОЗМОЖНОСТИ:
- 🚀 Асинхронная обработка до 5 документов одновременно
- 🤖 Автоматическое переключение: gemini-2.5-flash → gemini-2.5-flash-lite
- 💾 Кэширование в PostgreSQL (экономия до 100% на повторяющихся запросах)
- 📊 Детальная статистика использования и эффективности
- 🛡️ Устойчивость к сетевым ошибкам и rate limits
- 🔄 Повторная обработка пустых результатов
- 📈 Мониторинг производительности в реальном времени

АЛГОРИТМ РАБОТЫ:
1. Получение списка PDF файлов из базы данных
2. Разделение на пакеты по 5 страниц
3. Параллельная обработка в каждом пакете
4. OCR через Gemini API с rate limiting
5. Сохранение результатов в PostgreSQL
6. Повторная обработка при неудачах

МОДЕЛИ GEMINI:
- gemini-2.5-flash: 10 RPM / 500 RPD (основная, более мощная)
- gemini-2.5-flash-lite: 15 RPM / 1500 RPD (резервная, выше лимиты)

ОБРАБОТКА ОШИБОК:
- HTTP 429: Автоматическое переключение на резервную модель
- HTTP 503: Экспоненциальная задержка с повтором
- Timeout: Принудительная очистка rate limiter
- Пустые результаты: Повторная обработка (до 3 раз)

ПРОИЗВОДИТЕЛЬНОСТЬ:
- Rate limiting: 5 запросов/минуту (безопасный лимит)
- Пакетная обработка: 5 страниц одновременно
- Кэширование: Полное избежание дублированных запросов
- Мониторинг: Реальное время с автоматической очисткой зависших запросов

ЛОГИРОВАНИЕ:
- DEBUG: Детальная информация о каждом шаге
- INFO: Основные события и статистика
- WARNING: Предупреждения о проблемах
- ERROR: Критические ошибки с полным traceback

БАЗА ДАННЫХ:
- t_scan_documents_raw: Хранение OCR результатов
- t_gemini_cache: Кэш запросов к Gemini API
- t_grok_cache: Кэш запросов к Grok API
- t_billing_stats: Статистика использования API

МОНИТОРИНГ:
- Статистика rate limiter (запросы, задержки, эффективность)
- Счетчики успешных/неудачных обработок
- Время выполнения операций
- Автоматическая очистка зависших процессов
"""

import asyncio
import logging
import os
from os.path import exists
from pathlib import Path
from site import abs_paths
from typing import List, Dict, Any, Optional, Union
import time
from collections import deque

# Асинхронные библиотеки для I/O
import aiofiles
import aiofiles.os
import asyncpg  # Замена для psycopg2

# CPU-bound библиотеки, которые будут выполняться в отдельных потоках
import fitz  # PyMuPDF
from PIL import Image

from ExtractEmptyFiles import divide_documents, is_blank_async
# Импорты AI-функций
from Gemini.GeminiAI import extract_entity_from_file_async, get_current_model, get_model_limits_info
from Gemini.GoogleDocumentAI import google_ocr_documentai_async
from Mistral.MistalCorrectText import extract_data_from_text_by_mistral_async
from Mistral.MistralOCR import extract_ocr_data_by_mistral

from prompt import PROMPT_OCR_CONTROL, PROMPT_OCR

# --- Константы и Настройки ---
MAX_IMAGE_SIZE_MB = 19
MIN_DPI = 300
MAX_DPI = 500
DPI_STEP = 25
MAX_PIXEL_DIMENSION = 4900
MAX_JPEG_SIZE = 9000

# Настройки для Rate Limiting
GEMINI_MAX_REQUESTS_PER_MINUTE = 5  # RPM Немного меньше лимита в минуту для безопасности
GEMINI_REQUEST_INTERVAL = 60 / GEMINI_MAX_REQUESTS_PER_MINUTE  # секунд между запросами
MAX_CONCURRENT_TASKS = 5  # Максимальное количество одновременных задач
MAX_RETRIES = 3  # Максимальное количество повторных попыток
RETRY_BASE_DELAY = 2  # Базовая задержка для экспоненциального backoff

# Настройки для пакетной обработки
BATCH_SIZE = 1  # Количество страниц для обработки за один раз

# Настройка логирования
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Улучшенный sliding window rate limiter для Gemini API
class SmartRateLimiter:
    def __init__(self, max_requests_per_minute: int):
        self.max_requests = max_requests_per_minute
        self.request_times = deque()
        self.lock = asyncio.Lock()
        self.total_requests = 0
        self.throttled_requests = 0
        self.start_time = time.time()

    async def can_make_request(self):
        """Проверяет, можно ли сделать запрос без нарушения лимита"""
        async with self.lock:
            now = time.time()
            # Удаляем запросы старше 1 минуты
            while self.request_times and now - self.request_times[0] > 60:
                self.request_times.popleft()

            # Проверяем, не превышен ли лимит
            return len(self.request_times) < self.max_requests

    async def acquire(self):
        """Получает разрешение на запрос с умным ожиданием"""
        while True:
            if await self.can_make_request():
                async with self.lock:
                    self.request_times.append(time.time())
                    self.total_requests += 1
                return

            # Вычисляем время ожидания до восстановления лимита
            async with self.lock:
                if self.request_times:
                    oldest_request = self.request_times[0]
                    sleep_time = 60 - (time.time() - oldest_request) + 0.1  # +0.1 для безопасности
                    if sleep_time > 0:
                        self.throttled_requests += 1
                        logger.info(f"🎯 Rate limit: ждем {sleep_time:.1f} сек (запросов в очереди: {len(self.request_times)})")
                        await asyncio.sleep(sleep_time)
                    else:
                        # Лимит уже должен быть восстановлен, но проверка не прошла - возможно race condition
                        await asyncio.sleep(0.1)
                else:
                    # Очередь пуста, но проверка не прошла - редкий случай
                    await asyncio.sleep(0.1)

    async def release_on_error(self):
        """Освобождает место при ошибке"""
        async with self.lock:
            if self.request_times:
                self.request_times.pop()
                self.throttled_requests += 1

    def get_stats(self):
        """Получает статистику использования rate limiter"""
        elapsed = time.time() - self.start_time
        efficiency = (self.total_requests / max(self.total_requests + self.throttled_requests, 1)) * 100
        return {
            'total_requests': self.total_requests,
            'throttled_requests': self.throttled_requests,
            'efficiency': efficiency,
            'elapsed_time': elapsed,
            'current_queue_size': len(self.request_times)
        }

    async def reset_stats(self):
        """Сбрасывает статистику"""
        async with self.lock:
            self.total_requests = 0
            self.throttled_requests = 0
            self.start_time = time.time()

# Создаем глобальный rate limiter
gemini_rate_limiter = SmartRateLimiter(GEMINI_MAX_REQUESTS_PER_MINUTE)

# Функция для принудительной очистки rate limiter при зависании
async def force_reset_rate_limiter():
    """Принудительно сбрасывает rate limiter если система зависла"""
    async with gemini_rate_limiter.lock:
        old_count = len(gemini_rate_limiter.request_times)
        gemini_rate_limiter.request_times.clear()
        if old_count > 0:
            logger.warning(f"🔄 Принудительно очищен rate limiter: удалено {old_count} зависших запросов")

# --- Параметры подключения к БД (из переменных окружения) ---
DB_PARAMS = {
    'host': os.getenv("PG_HOST_LOCAL", "localhost"),
    'database': os.getenv("PG_DBNAME", "postgres"),
    'user': os.getenv("PG_USER", "postgres"),
    'password': os.getenv("PG_PASSWORD", ""),
    'port': int(os.getenv("PG_PORT", "5432"))
}

# Функция для безопасного выполнения запросов к Gemini API
async def safe_gemini_request(file_path: str, prompt: str, max_retries: int = MAX_RETRIES, timeout: int = 60) -> str:
    """
    Безопасно выполняет запрос к Gemini API с rate limiting, retry логикой и таймаутом.
    """
    logger.debug(f"🚀 Начинаем safe_gemini_request для {Path(file_path).name}")

    for attempt in range(max_retries):
        acquired = False
        try:
            logger.debug(f"🔄 Попытка {attempt + 1}/{max_retries} для {Path(file_path).name}")

            # Ждем разрешения от rate limiter
            logger.debug(f"⏳ Ожидаем разрешения от rate limiter для {Path(file_path).name}")
            await gemini_rate_limiter.acquire()
            acquired = True
            logger.debug(f"✅ Разрешение получено от rate limiter для {Path(file_path).name}")

            logger.info(f"📡 Выполняется запрос к Gemini API для {Path(file_path).name}, попытка {attempt + 1}")

            # Выполняем запрос с таймаутом
            logger.debug(f"🔍 Вызываем extract_entity_from_file_async для {Path(file_path).name}")
            result = await asyncio.wait_for(
                extract_entity_from_file_async(file_path, prompt),
                timeout=timeout
            )
            logger.debug(f"📥 Получен ответ от Gemini API для {Path(file_path).name}")

            if result and len(result.strip()) > 0:
                logger.info(f"✅ Успешный запрос к Gemini API для {Path(file_path).name}, длина ответа: {len(result)} символов")
                return result
            else:
                logger.warning(f"⚠️ Пустой ответ от Gemini API для {Path(file_path).name}, попытка {attempt + 1}")
                # При пустом ответе освобождаем место для повтора
                await gemini_rate_limiter.release_on_error()
                acquired = False

        except asyncio.TimeoutError:
            logger.error(f"⏰ Таймаут ({timeout}с) при запросе к Gemini API для {Path(file_path).name}, попытка {attempt + 1}")
            # При таймауте освобождаем место для повтора
            if acquired:
                await gemini_rate_limiter.release_on_error()
                acquired = False
            # Прерываем попытки при таймауте - не тратим время на повторы
            break

        except Exception as e:
            error_msg = str(e)

            # При любой ошибке освобождаем место
            if acquired:
                await gemini_rate_limiter.release_on_error()
                acquired = False

            # Проверяем, является ли это ошибкой rate limit
            if "429" in error_msg or "quota" in error_msg.lower():
                retry_delay = RETRY_BASE_DELAY * (2 ** attempt)  # Экспоненциальная задержка
                logger.warning(f"🚫 Rate limit для Gemini API. Повтор через {retry_delay} сек. Попытка {attempt + 1}/{max_retries}")
                await asyncio.sleep(retry_delay)
                continue
            else:
                logger.error(f"❌ Ошибка Gemini API для {Path(file_path).name}: {error_msg}")
                break

    logger.error(f"💥 Не удалось получить результат от Gemini API для {Path(file_path).name} после {max_retries} попыток")
    return ""


def get_file_extension(file_path: str) -> str:
    return Path(file_path).suffix.lower().lstrip('.')


def _save_pdf_page_as_pdf_sync_worker(input_path: str, page_number: int) -> str:
    """
    Синхронный воркер для CPU-bound операций. Выполняется в to_thread.
    Сохраняет страницу как отдельный PDF файл.
    """
    output_dir = "temp_pdf"
    os.makedirs(output_dir, exist_ok=True)

    doc = fitz.open(input_path)
    try:
        if not (1 <= page_number <= len(doc)):
            raise ValueError(f"Страница {page_number} отсутствует в документе (всего {len(doc)} стр.).")

        # Создаем новый PDF документ с одной страницей
        new_doc = fitz.open()
        new_doc.insert_pdf(doc, from_page=page_number-1, to_page=page_number-1)

        base_name = f"{Path(input_path).stem}_page_{page_number}.pdf"
        pdf_path = os.path.join(output_dir, base_name)
        new_doc.save(pdf_path)
        new_doc.close()

        return pdf_path
    finally:
        doc.close()


async def save_pdf_page_as_pdf_async(input_path: str, page_number: int) -> Optional[str]:
    """
    Асинхронно извлекает страницу из PDF и сохраняет как отдельный PDF файл.
    """
    if not await aiofiles.os.path.exists(input_path):
        logger.error(f"Входной файл не существует: {input_path}")
        return None
    try:
        # Выносим всю синхронную, CPU-bound работу в отдельный поток
        pdf_path = await asyncio.to_thread(
            _save_pdf_page_as_pdf_sync_worker,
            input_path, page_number
        )
        logger.info(f"Сохранена страница {page_number} как PDF: {pdf_path}")
        return pdf_path
    except Exception as e:
        logger.error(f"Ошибка при сохранении страницы {page_number} из файла {input_path}: {e}", exc_info=True)
        return None


# ==============================================================================
# РАБОТА С БАЗОЙ ДАННЫХ (asyncpg)
# ==============================================================================

async def add_to_db_async(data: List[Dict[str, Any]]):
    """
    Асинхронно добавляет данные в базу данных, используя asyncpg.
    """
    if not data:
        return 0

    sql = """
        INSERT INTO t_scan_documents_raw (full_path, page_number, description, file_name, model_ai, created_at)
        VALUES ($1, $2, $3, $4, $5, now())
        ON CONFLICT (file_name, page_number) DO UPDATE
        SET description = EXCLUDED.description,
            full_path = EXCLUDED.full_path,
            model_ai = EXCLUDED.model_ai,
            created_at = now();
    """
    values = [
        (
            doc.get('full_path'),
            doc.get('page_number'),
            doc.get('description'),
            doc.get('file_name'),
            doc.get('model_ai')
        ) for doc in data
    ]

    conn = None
    successful_count = 0
    try:
        conn = await asyncpg.connect(**DB_PARAMS)
        # executemany для массовой вставки при ON CONFLICT НЕ РАБОТАЕТ
        # Обрабатываем каждую запись отдельно для сохранения максимального количества данных
        for i, value in enumerate(values):
            try:
                await conn.execute(sql, *value)
                successful_count += 1
            except Exception as record_error:
                logger.warning(f"Ошибка при сохранении записи {i+1}/{len(values)}: {record_error}")
                continue

        logger.info(f"Данные OCR: успешно сохранено {successful_count}.")
        return successful_count
    except Exception as e:
        logger.error(f"Критическая ошибка при подключении к БД: {e}", exc_info=True)
        return successful_count
    finally:
        if conn:
            await conn.close()


async def process_image_async(pdf_or_image_path: str, page_number: int, max_retries: int = 3) -> Dict[str, Any]:
    """
    Асинхронно обрабатывает одну страницу/изображение: сохраняет, делает OCR, очищает и пишет в БД.
    ГАРАНТИРУЕТ, что каждая страница сохраняется ровно 1 раз, даже при ошибках ИИ.
    """
    data = {
        'full_path': pdf_or_image_path,
        'page_number': page_number,
        'description': None,
        'file_name': Path(pdf_or_image_path).name,
        'model_ai': get_current_model()  # Динамическая модель из GeminiAI
    }
    temp_file_name = None
    processing_successful = False

    try:
        # Проверяем существование файла перед обработкой
        if not await aiofiles.os.path.exists(pdf_or_image_path):
            logger.warning(f"Файл для обработки не найден: {pdf_or_image_path}")
            # Даже если файл не найден, сохраняем запись с ошибкой
            data['description'] = "ОШИБКА: Файл не найден"
            await add_to_db_async([data])
            return data

        temp_file_name = None
        ext = get_file_extension(pdf_or_image_path)
        if ext in ['png', 'jpg', 'jpeg', 'bmp', 'tiff']:
            temp_file_name = pdf_or_image_path
        elif ext == 'pdf':
            temp_file_name = await save_pdf_page_as_pdf_async(pdf_or_image_path, page_number)
        else:
            logger.warning(f"Неподдерживаемый формат файла для OCR: {pdf_or_image_path} {ext}")
            # Сохраняем с информацией об ошибке
            data['description'] = f"ОШИБКА: Неподдерживаемый формат файла {ext}"
            await add_to_db_async([data])
            return data

        if not temp_file_name or not exists(temp_file_name):
            logger.error("Не удалось создать временный файл.")
            # Сохраняем с информацией об ошибке
            data['description'] = "ОШИБКА: Не удалось создать временный файл"
            await add_to_db_async([data])
            return data

        # Пытаемся обработать страницу с повторными попытками (БЕЗ РЕКУРСИИ)
        for attempt in range(max_retries):
            logger.info(f"🔍 OCR попытка {attempt + 1}/{max_retries} для {data['file_name']} стр.{page_number}")

            try:
                ocr_text = await safe_gemini_request(temp_file_name, PROMPT_OCR_CONTROL)

                if ocr_text and len(ocr_text.strip()) > 100:
                    # Успешная обработка
                    data['description'] = ocr_text
                    logger.info(f"✅ Успешно обработана {data['file_name']} стр.{page_number}")
                    processing_successful = True
                    break
                else:
                    logger.warning(f"⚠️ Попытка {attempt + 1} неудачна для {data['file_name']} стр.{page_number} - пустой или короткий ответ")

            except Exception as attempt_error:
                logger.warning(f"⚠️ Ошибка в попытке {attempt + 1} для {data['file_name']} стр.{page_number}: {attempt_error}")

        # Если после всех попыток результат пустой, проверяем, является ли страница пустой
        if not data['description'] and ext == 'pdf':
            try:
                doc = await asyncio.to_thread(fitz.open, pdf_or_image_path)
                if 1 <= page_number <= len(doc):
                    page = doc.load_page(page_number - 1)
                    is_page_blank = await is_blank_async(page)
                    if is_page_blank:
                        logger.info(f"📄 Страница {page_number} в {data['file_name']} является пустой")
                        data['description'] = "ПУСТАЯ СТРАНИЦА"
                        processing_successful = True
                    else:
                        data['description'] = "ОШИБКА: ИИ не вернул результат, страница не пустая"
                doc.close()
            except Exception as e:
                logger.warning(f"Не удалось проверить пустоту страницы {page_number}: {e}")
                data['description'] = "ОШИБКА: Не удалось проверить пустоту страницы"

        # Если все еще нет результата, устанавливаем сообщение об ошибке
        if not data['description']:
            data['description'] = "ОШИБКА: ИИ не вернул результат после всех попыток"
            logger.error(f"💥 Все попытки обработки страницы {page_number} в {data['file_name']} неудачны")

        # ГАРАНТИРОВАННО сохраняем результат в БД
        logger.info(f"💾 Сохраняем в БД данные для {data['file_name']} стр.{page_number}")
        save_result = await add_to_db_async([data])

        if save_result > 0:
            logger.info(f"✅ Данные сохранены в БД для {data['file_name']} стр.{page_number}")
        else:
            logger.error(f"❌ Ошибка сохранения в БД для {data['file_name']} стр.{page_number}")

        return data

    except Exception as e:
        logger.error(f"Критическая ошибка при обработке {pdf_or_image_path} стр.{page_number}: {e}", exc_info=True)

        # Даже при критической ошибке пытаемся сохранить информацию в БД
        try:
            data['description'] = f"КРИТИЧЕСКАЯ ОШИБКА: {str(e)}"
            await add_to_db_async([data])
            logger.info(f"💾 Информация об ошибке сохранена в БД для {data['file_name']} стр.{page_number}")
        except Exception as save_error:
            logger.error(f"❌ Не удалось сохранить даже информацию об ошибке: {save_error}")

        return data

    finally:
        # Удаляем временный файл, если он был создан из PDF
        if temp_file_name and get_file_extension(pdf_or_image_path) == 'pdf' and await aiofiles.os.path.exists(temp_file_name):
            try:
                await aiofiles.os.remove(temp_file_name)
            except Exception as e:
                logger.warning(f"Не удалось удалить временный файл {temp_file_name}: {e}")


async def extract_pages_from_pdf_async(pdf_or_image_path: str, page_numbers: Optional[List[int]] = None):
    """
    Асинхронно извлекает данные со страниц PDF или изображения.
    Обрабатывает страницы пакетами по 15 страниц за раз.
    """
    if not await aiofiles.os.path.exists(pdf_or_image_path):
        logger.error(f"Файл не найден: {pdf_or_image_path}")
        return

    if not page_numbers:
        # Если номера страниц не указаны, получаем их количество
        try:
            # Это быстрая синхронная операция, можно выполнить напрямую или в to_thread для 100% неблокировки
            doc = await asyncio.to_thread(fitz.open, pdf_or_image_path)
            total_pages = len(doc)
            doc.close()
            page_numbers = list(range(1, total_pages + 1))
        except Exception as e:
            logger.error(f"Не удалось прочитать PDF файл {pdf_or_image_path}: {e}")
            return

    # Обрабатываем страницы пакетами по BATCH_SIZE страниц
    successful_results = []
    total_pages = len(page_numbers)

    logger.info(f"📄 Начинаем обработку {total_pages} страниц пакетами по {BATCH_SIZE}")

    for batch_start in range(0, total_pages, BATCH_SIZE):
        batch_end = min(batch_start + BATCH_SIZE, total_pages)
        batch_pages = page_numbers[batch_start:batch_end]

        logger.info(f"🔄 Обрабатываем пакет {batch_start//BATCH_SIZE + 1}/{(total_pages + BATCH_SIZE - 1)//BATCH_SIZE}: страницы {batch_pages[0]}-{batch_pages[-1]}")

        # Обрабатываем страницы в пакете параллельно
        tasks = []
        for page_num in batch_pages:
            task = asyncio.create_task(
                asyncio.wait_for(
                    process_image_async(pdf_or_image_path, page_num),
                    timeout=180  # 3 минуты на одну страницу
                )
            )
            tasks.append((page_num, task))

        # Ждем завершения всех задач в пакете
        for page_num, task in tasks:
            try:
                result = await task
                if result:
                    successful_results.append(result)
                    logger.info(f"✅ Страница {page_num} обработана успешно")
                else:
                    logger.warning(f"⚠️ Страница {page_num} - пустой результат")

            except asyncio.TimeoutError:
                logger.error(f"⏰ Страница {page_num} - таймаут (3 мин)")
                # Принудительно очищаем rate limiter при таймауте
                await force_reset_rate_limiter()

            except Exception as e:
                logger.error(f"❌ Страница {page_num} - ошибка: {e}")

        logger.info(f"✅ Пакет {batch_start//BATCH_SIZE + 1} завершен. Обработано страниц: {len(batch_pages)}")

    logger.info(f"📊 Обработка завершена. Всего обработано: {len(successful_results)}/{total_pages} страниц")
    return {'doc': successful_results}


async def extract_pdf_files_from_folder_async(folder_path: str):
    """
    Асинхронно находит все PDF в папке и обрабатывает их с ограничением параллельности.
    """
    pdf_files = []
    # os.walk - синхронный генератор, его можно оставить, т.к. он быстрый
    for root, _, files in os.walk(folder_path):
        for file in files:
            abs_path = os.path.abspath(os.path.join(root, file))
            if file.lower().endswith('.pdf') and not file.lower().endswith('data.pdf') and not file.lower().endswith('empty.pdf'): 
                abs_path, pdf_empty = await divide_documents(abs_path)  # удаляем из документа пустые страницы.
            pdf_files.append(abs_path) # pdf_with_data - имя файла с данными, pdf_empty - имя файла с пустыми страницами

    logger.info(f"Найдено {len(pdf_files)} PDF файлов для обработки")

    # Обрабатываем файлы последовательно
    successful = 0
    failed = 0

    for i, pdf_file in enumerate(pdf_files):
        try:
            logger.info(f"🔄 Обрабатываем файл {i+1}/{len(pdf_files)}: {Path(pdf_file).name}")

            # Обрабатываем без таймаута
            result = await extract_pages_from_pdf_async(pdf_file)

            if result:
                successful += 1
                logger.info(f"✅ Файл {Path(pdf_file).name} обработан успешно")
            else:
                failed += 1
                logger.warning(f"⚠️ Файл {Path(pdf_file).name} - пустой результат")

        except Exception as e:
            failed += 1
            logger.error(f"❌ Файл {Path(pdf_file).name} - ошибка: {e}")

    logger.info(f"Обработка завершена. Успешно: {successful}, с ошибками: {failed}")


async def monitor_with_timeout(duration_minutes: int = 30):
    """Мониторинг с ограничением по времени"""
    end_time = time.time() + (duration_minutes * 60)

    while time.time() < end_time:
        await asyncio.sleep(180)
        logger.warning("🔄 Принудительная проверка системы...")
        await force_reset_rate_limiter()
        logger.info("🔄 Rate limiter очищен.")

    logger.info(f"Мониторинг завершен после {duration_minutes} минут")


async def reprocess_empty_descriptions():
    """
    Повторно обрабатывает записи с пустым description из базы данных.
    """
    conn = None
    try:
        conn = await asyncpg.connect(**DB_PARAMS)
        
        # Получаем записи с пустым description
        query = r"""
            -- Извлекаем записи с отсутствующими страницами. НЕ УДАЛЯТЬ
            WITH pages AS (
                SELECT file_name,
                    max(page_number) AS max_page_number
                FROM t_scan_documents_raw
                GROUP BY file_name
            ),
            missing AS (
                SELECT p.file_name, gs AS page_number
                FROM pages p
                CROSS JOIN LATERAL generate_series(1, p.max_page_number) gs
                WHERE NOT EXISTS (
                    SELECT 1
                    FROM t_scan_documents_raw t
                    WHERE t.file_name = p.file_name
                    AND t.page_number = gs
                )
            )
            SELECT *, concat('C:\Scan\All\AlreadyAddToDb\', file_name) as full_path
            FROM missing
            UNION ALL
            -- Извлекаем записи с пустым description
            SELECT file_name, page_number, full_path
            FROM t_scan_documents_raw
            WHERE description IS NULL OR trim(description) = ''
            ORDER BY file_name, page_number;
            """
        
        rows = await conn.fetch(query)
        logger.info(f"Найдено {len(rows)} записей для повторной обработки")
        
        if not rows:
            logger.info("Нет записей для повторной обработки")
            return

        successful = 0
        failed = 0

        for i, row in enumerate(rows):
            try:
                logger.info(f"🔄 Обрабатываем запись {i+1}/{len(rows)}")

                # Основной путь (жестко прописанный)
                file_path = os.path.join(r"c:\Scan\All\AlreadyAddToDb\t", row['file_name'])

                # Резервный путь из БД, если основной не найден
                if not os.path.exists(file_path):
                    alt_path = row['full_path']
                    if os.path.exists(alt_path):
                        file_path = alt_path
                        logger.info(f"📁 Используем путь из БД: {alt_path}")
                    else:
                        logger.error(f"❌ Файл не найден: {file_path} и {alt_path}")
                        continue

                # Обрабатываем с таймаутом и правильными параметрами
                result = await asyncio.wait_for(
                    process_image_async(file_path, row['page_number'], max_retries=3),
                    timeout=180  # 3 минуты на одну запись
                )

                # Правильная проверка результата
                if result and isinstance(result, dict) and result.get('description'):
                    successful += 1
                    logger.info(f"✅ Запись {i+1}/{len(rows)} обработана успешно")
                else:
                    failed += 1
                    logger.warning(f"⚠️ Запись {i+1}/{len(rows)} - пустой результат")

            except asyncio.TimeoutError:
                failed += 1
                logger.error(f"⏰ Запись {i+1}/{len(rows)} - таймаут (3 мин)")
                # Принудительно очищаем rate limiter при таймауте
                await force_reset_rate_limiter()

            except Exception as e:
                failed += 1
                logger.error(f"❌ Запись {i+1}/{len(rows)} - ошибка: {e}")

        # Выводим финальную статистику
        logger.info(f"Повторная обработка завершена. Успешно: {successful}, с ошибками: {failed}")

    except Exception as e:
        logger.error(f"Ошибка при получении данных из БД для переобработки: {e}", exc_info=True)
    finally:
        if conn:
            await conn.close()


# ==============================================================================
# ТОЧКА ВХОДА
# ==============================================================================

async def main():
    """Главная асинхронная функция."""
    if os.name == "nt":
        os.system("cls")
    else:
        os.system("clear")

    logger.info("=== Запуск системы обработки документов ===")
    logger.info(f"Настройки Rate Limiting: {GEMINI_MAX_REQUESTS_PER_MINUTE} запросов/мин")
    logger.info(f"Максимальное количество параллельных задач: {MAX_CONCURRENT_TASKS}")

    start_time = time.time()

    # --- Выберите один из вариантов для запуска ---
    pdf_file_path = r"c:\Scan\All\AlreadyAddToDb\2025.06 Vozvrati 1.pdf" 

    # Вариант 1. Обработка всех pdf в папке с учетом вложенных.
    # logger.info("Запуск варианта 1: Обработка всех PDF в папке...")
    # folder_path = r"C:\Scan\All\AlreadyAddToDb\t"
    # await extract_pdf_files_from_folder_async(folder_path)

    # Вариант 2. Обработка одного pdf с указанием списка страниц.
    # logger.info("Запуск варианта 2: Обработка одного PDF с указанием страниц...")

    # empty_pages = [
    #     {'pdf_file_path': r'c:\Scan\All\AlreadyAddToDb\2024.10 Vozvrati 3.pdf', 'page_numbers': [ 6, 10, 14, 15, 18 ]},
    #     {'pdf_file_path': r'c:\Scan\All\AlreadyAddToDb\2024.10 Vozvrati 4.pdf', 'page_numbers': [ 5, 8, 12, 16, 20, 23, 24, 27, 28, 29 ]},
    # ]
    
    # for item in empty_pages:
    #     pdf_file_path_raw = item['pdf_file_path']
    #     page_numbers = item['page_numbers']
    #     pdf_file_path = divide_documents(pdf_file_path_raw)
    #     # page_numbers = [1]
    #     result = await extract_pages_from_pdf_async(pdf_file_path, page_numbers)
    #     print(result)

    # Вариант 3. Обработка одного pdf БЕЗ указания списка страниц.
    # logger.info("Запуск варианта 3: Обработка всех страниц одного PDF...")
    # result = await extract_pages_from_pdf_async(pdf_file_path)
    # print(result)

    # Вариант 4. Повторная обработка записей из БД.
    # logger.info("Запуск варианта 4: Повторная обработка данных из БД...")

    # Запускаем мониторинг в фоне
    monitor_task = asyncio.create_task(monitor_with_timeout(30))

    # Запускаем обработку без таймаута
    await reprocess_empty_descriptions()

    # Отменяем мониторинг если обработка завершилась
    monitor_task.cancel()
    try:
        await monitor_task
    except asyncio.CancelledError:
        pass

    elapsed_time = time.time() - start_time

    # Выводим статистику rate limiter
    stats = gemini_rate_limiter.get_stats()
    logger.info(f"Все операции завершены. Время выполнения: {elapsed_time:.2f} секунд")
    logger.info("📊 СТАТИСТИКА RATE LIMITER:")
    logger.info(f"   Всего запросов: {stats['total_requests']}")
    logger.info(f"   Ожиданий лимита: {stats['throttled_requests']}")
    logger.info(f"   Эффективность: {stats['efficiency']:.1f}%")
    logger.info(f"   Время работы: {stats['elapsed_time']:.1f} сек")
    logger.info(f"   Текущая очередь: {stats['current_queue_size']}")


if __name__ == "__main__":
    # Запускаем асинхронную программу
    asyncio.run(main())
