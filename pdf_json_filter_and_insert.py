"""
СКРИПТ ФИЛЬТРАЦИИ JSON ДАННЫХ И ЗАГРУЗКИ В POSTGRESQL

Этот скрипт предназначен для обработки JSON файлов с результатами OCR,
фильтрации данных по колонкам таблицы t_scan_documents_raw и загрузки
в базу данных PostgreSQL.

ОСНОВНЫЕ ФУНКЦИИ:
1. Чтение JSON файлов из указанной папки
2. Фильтрация данных - только колонки, совпадающие с таблицей
3. Загрузка отфильтрованных данных в PostgreSQL
4. Обработка ошибок парсинга JSON

СТРУКТУРА ТАБЛИЦЫ t_scan_documents_raw:
- full_path: текст
- page_number: целое число
- description: текст
- file_name: текст
- created_at: timestamp (автоматически)

ФИЛЬТРАЦИЯ:
Скрипт берет из JSON только те поля, которые есть в таблице БД.
"""

import glob
import json
import os
from dotenv import load_dotenv
import psycopg2
from psycopg2 import sql

# Загрузка переменных окружения
load_dotenv()

# Путь к папке с JSON файлами
folder_path = r'C:\Scan\All\AlreadyAddToDb\*.json'

# Колонки таблицы для фильтрации
TABLE_COLUMNS = ['full_path', 'page_number', 'description', 'file_name']

def load_json_safely(file_path):
    """
    БЕЗОПАСНАЯ ЗАГРУЗКА JSON ФАЙЛА С ОБРАБОТКОЙ ОШИБОК

    Пытается загрузить JSON файл, обрабатывая различные форматы и ошибки.
    Извлекает массив данных из поля 'doc', если оно существует.
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read().strip()

            # Пробуем загрузить как обычный JSON
            try:
                data = json.loads(content)

                # Если данные в поле 'doc', извлекаем их
                if isinstance(data, dict) and 'doc' in data:
                    doc_data = data['doc']
                    if isinstance(doc_data, list):
                        return doc_data
                    else:
                        print(f"⚠️  Поле 'doc' не содержит массив в файле: {os.path.basename(file_path)}")
                        return []

                # Если данные уже в массиве, возвращаем как есть
                elif isinstance(data, list):
                    return data

                # Если данные в объекте, но без поля 'doc', возвращаем пустой список
                else:
                    print(f"⚠️  Неподдерживаемая структура JSON в файле: {os.path.basename(file_path)}")
                    return []

            except json.JSONDecodeError:
                # Пробуем обработать нестандартный формат с "doc": [
                if content.strip().startswith('"doc": ['):
                    try:
                        # Преобразуем в правильный JSON формат
                        json_content = '{' + content.strip() + '}'
                        data = json.loads(json_content)

                        if isinstance(data, dict) and 'doc' in data:
                            doc_data = data['doc']
                            if isinstance(doc_data, list):
                                return doc_data
                            else:
                                print(f"⚠️  Поле 'doc' не содержит массив в файле: {os.path.basename(file_path)}")
                                return []
                        else:
                            print(f"⚠️  Не удалось преобразовать нестандартный JSON в файле: {os.path.basename(file_path)}")
                            return []
                    except json.JSONDecodeError:
                        # Пробуем альтернативный подход - найти конец массива
                        try:
                            # Ищем позицию закрывающей скобки массива
                            start_pos = content.find('[')
                            if start_pos != -1:
                                # Считаем скобки для нахождения правильного конца
                                bracket_count = 0
                                end_pos = -1
                                for i in range(start_pos, len(content)):
                                    if content[i] == '[':
                                        bracket_count += 1
                                    elif content[i] == ']':
                                        bracket_count -= 1
                                        if bracket_count == 0:
                                            end_pos = i
                                            break

                                if end_pos != -1:
                                    # Извлекаем массив и парсим его
                                    array_content = content[start_pos:end_pos + 1]
                                    data = json.loads(array_content)
                                    if isinstance(data, list):
                                        return data
                                    else:
                                        print(f"⚠️  Извлеченные данные не являются массивом в файле: {os.path.basename(file_path)}")
                                        return []
                        except json.JSONDecodeError:
                            pass

                # Если обычный JSON не работает, пробуем другие форматы
                pass

            # Проверяем на формат с префиксом (например, "data: [...]")
            if ':' in content[:20]:
                # Ищем начало JSON массива или объекта
                start_idx = content.find('[')
                if start_idx == -1:
                    start_idx = content.find('{')
                if start_idx != -1:
                    try:
                        return json.loads(content[start_idx:])
                    except json.JSONDecodeError:
                        pass

            # Если ничего не помогло, возвращаем пустой список
            print(f"⚠️  Не удалось распарсить JSON в файле: {os.path.basename(file_path)}")
            return []

    except Exception as e:
        print(f"❌ Ошибка чтения файла {os.path.basename(file_path)}: {e}")
        return []

def filter_json_data(data, file_path):
    """
    ФИЛЬТРАЦИЯ ДАННЫХ JSON ПО КОЛОНКАМ ТАБЛИЦЫ

    Оставляет только те поля, которые соответствуют колонкам таблицы.
    Использует id как page_number, если page_number отсутствует.
    """
    filtered_data = []

    for item in data:
        if isinstance(item, dict):
            # Фильтруем только нужные поля
            filtered_item = {}

            # Обрабатываем каждое поле таблицы
            for key in TABLE_COLUMNS:
                if key == 'page_number':
                    # Используем id как page_number, если page_number отсутствует
                    if 'page_number' in item:
                        filtered_item[key] = item['page_number']
                    elif 'id' in item:
                        filtered_item[key] = item['id']
                    else:
                        filtered_item[key] = 1  # значение по умолчанию
                elif key == 'full_path':
                    # Используем full_path из JSON или путь к файлу
                    if 'full_path' in item:
                        filtered_item[key] = item['full_path']
                    else:
                        filtered_item[key] = file_path  # реальный путь к файлу
                elif key in item:
                    filtered_item[key] = item[key]

            # Добавляем только если есть хотя бы file_name и description
            if 'file_name' in filtered_item and 'description' in filtered_item:
                filtered_data.append(filtered_item)

    return filtered_data

def insert_filtered_data(data):
    """
    ЗАГРУЗКА ОТФИЛЬТРОВАННЫХ ДАННЫХ В POSTGRESQL
    """
    if not data:
        print("📭 Нет данных для загрузки")
        return

    print(f"📊 Загружаем {len(data)} записей в базу данных...")

    # Подключение к БД
    conn = psycopg2.connect(
        dbname=os.getenv('PG_DBNAME'),
        user=os.getenv('PG_USER'),
        password=os.getenv('PG_PASSWORD'),
        host=os.getenv('PG_HOST_LOCAL'),
        port=os.getenv('PG_PORT')
    )

    cursor = conn.cursor()

    inserted_count = 0
    updated_count = 0
    error_count = 0

    for i, item in enumerate(data, 1):
        try:
            # Извлекаем значения с проверкой
            full_path = item.get('full_path', item.get('file_name', ''))
            page_number = item.get('page_number', 1)
            description = item.get('description', '')
            file_name = item.get('file_name', '')

            # Вставка с обработкой конфликтов
            cursor.execute("""
                INSERT INTO public.t_scan_documents_raw (full_path, page_number, description, file_name)
                VALUES (%s, %s, %s, %s)
                ON CONFLICT (file_name, page_number)
                DO UPDATE SET
                    full_path = EXCLUDED.full_path,
                    description = EXCLUDED.description,
                    created_at = now()
            """, (full_path, page_number, description, file_name))

            inserted_count += 1

            if i % 100 == 0:
                print(f"📈 Обработано {i}/{len(data)} записей...")

        except Exception as e:
            error_count += 1
            print(f"❌ Ошибка при обработке записи {i}: {e}")
            continue

    # Фиксация и закрытие
    conn.commit()
    cursor.close()
    conn.close()

    print("✅ Данные успешно загружены!")
    print(f"📊 Статистика: {inserted_count} записей обработано, {error_count} ошибок")

def main():
    """
    ГЛАВНАЯ ФУНКЦИЯ СКРИПТА
    """
    print("=" * 60)
    print("🚀 ЗАПУСК СИСТЕМЫ ФИЛЬТРАЦИИ JSON И ЗАГРУЗКИ В POSTGRESQL")
    print("=" * 60)

    try:
        all_filtered_data = []
        problematic_files = []  # Список проблемных файлов

        # Обработка каждого JSON файла
        for file_path in glob.glob(folder_path):
            file_name = os.path.basename(file_path)
            print(f"\n📂 Обрабатываем файл: {file_name}")

            # Загружаем и фильтруем данные
            raw_data = load_json_safely(file_path)
            filtered_data = filter_json_data(raw_data, file_path)

            # Проверяем, удалось ли обработать файл
            if isinstance(raw_data, list) and len(raw_data) > 0:
                if len(filtered_data) == 0:
                    problematic_files.append(f"{file_name} - нет подходящих данных")
                else:
                    print(f"✅ Отфильтровано {len(filtered_data)} записей из {len(raw_data)}")
            else:
                problematic_files.append(f"{file_name} - ошибка парсинга JSON")
                print(f"✅ Отфильтровано {len(filtered_data)} записей из N/A")

            all_filtered_data.extend(filtered_data)

        # Загружаем все отфильтрованные данные в БД
        print(f"\n💾 Загружаем {len(all_filtered_data)} записей в базу данных...")
        insert_filtered_data(all_filtered_data)

        print("\n" + "=" * 60)
        print("✅ ВСЕ ОПЕРАЦИИ ВЫПОЛНЕНЫ УСПЕШНО!")
        print("=" * 60)

        # ВЫВОД СПИСКА ПРОБЛЕМНЫХ ФАЙЛОВ
        if problematic_files:
            print("\n" + "!" * 60)
            print("⚠️  СПИСОК ПРОБЛЕМНЫХ ФАЙЛОВ:")
            print("!" * 60)
            for i, file_info in enumerate(problematic_files, 1):
                print(f"{i}. {file_info}")
            print(f"\n📊 Всего проблемных файлов: {len(problematic_files)}")
        else:
            print("\n✅ Все файлы обработаны успешно!")

    except Exception as e:
        print("\n" + "!" * 60)
        print("❌ КРИТИЧЕСКАЯ ОШИБКА ВЫПОЛНЕНИЯ!")
        print(f"📝 Описание ошибки: {e}")
        print("!" * 60)

if __name__ == "__main__":
    main()
