"""
ProcessingPdf.py
Извлекает из input.pdf страницы и сохраняет в output.pdf с учетом переданного DPI и сжатия
"""
import fitz  # PyMuPDF
import os
import logging
from typing import List, Tuple, Dict

# Настройка логирования
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def is_scan(page: fitz.Page) -> bool:
    """Определяет, является ли страница сканом (изображением)."""
    if page.get_text().strip():  # Если есть текст — не скан
        return False
    images = page.get_images()
    return len(images) > 0


def extract_and_compress_pages(
        input_path: str,
        output_path: str,
        page_numbers: List[int],
        dpi: int = 200,
        jpeg_quality: int = 20,
) -> Tuple[bool, Dict]:
    """
    Извлекает указанные страницы из PDF, применяет сжатие и сохраняет в новый файл.

    Args:
        input_path: Путь к исходному PDF.
        output_path: Путь для сохранения нового PDF.
        page_numbers: Номера страниц для извлечения (нумерация с 1).
        dpi: Разрешение для сканов.
        jpeg_quality: Качество JPEG (1-100).

    Returns:
        (успех, информация о сжатии)
    """
    if not os.path.exists(input_path):
        return False, {"error": "Входной файл не существует"}

    try:
        doc = fitz.open(input_path)
        new_doc = fitz.open()  # Новый PDF

        total_pages = len(doc)
        scan_pages = 0
        vector_pages = 0

        # Проверяем, что запрошенные страницы существуют
        if not page_numbers:
            page_numbers = [p for p in range(1, total_pages + 1)]

        invalid_pages = [p for p in page_numbers if p < 1 or p > total_pages]

        if invalid_pages:
            return False, {"error": f"Страницы {invalid_pages} не существуют в документе"}

        for page_num in page_numbers:
            page = doc.load_page(page_num - 1)  # Нумерация с 0

            if is_scan(page):
                # Для сканов: рендерим с новым DPI и качеством
                pix = page.get_pixmap(dpi=dpi)
                img_bytes = pix.tobytes("jpeg", jpeg_quality)
                new_page = new_doc.new_page(width=page.rect.width, height=page.rect.height)
                new_page.insert_image(page.rect, stream=img_bytes)
                scan_pages += 1
                logger.info(f"Страница {page_num}: скан (DPI={dpi}, качество={jpeg_quality}%)")
            else:
                # Для текста/векторов: копируем без изменений
                new_doc.insert_pdf(doc, from_page=page_num - 1, to_page=page_num - 1)
                vector_pages += 1
                logger.info(f"Страница {page_num}: текст/вектор (оригинальное качество)")

        # Оптимизация и сохранение
        new_doc.save(
            output_path,
            garbage=4,
            deflate=True,
            clean=True,
        )

        # Статистика
        input_size = os.path.getsize(input_path) / 1024  # KB
        output_size = os.path.getsize(output_path) / 1024
        reduction = (1 - output_size / input_size) * 100 if input_size > 0 else 0

        logger.info(
            f"Извлечено страниц: {len(page_numbers)}\n"
            f"Сканированных: {scan_pages}, текстовых/векторных: {vector_pages}\n"
            f"Размер нового файла: {output_size:.2f} KB"
        )

        return True, {
            "processed_pages": len(page_numbers),
            "scan_pages": scan_pages,
            "vector_pages": vector_pages,
            "output_size": output_size,
        }

    except Exception as e:
        logger.error(f"Ошибка: {str(e)}", exc_info=True)
        return False, {"error": str(e)}
    finally:
        if 'doc' in locals():
            doc.close()
        if 'new_doc' in locals():
            new_doc.close()


# Пример использования
if __name__ == "__main__":
    
    # цикл по каждому файлу в папке
    for root, _, files in os.walk(r"C:\Scan\All\ForParse\32294905_ЕКСПАНСІЯ\ТТН\н"):
        for file in files:
            if file.lower().endswith('.pdf') and 'compressed' not in file.lower():
                input_path = os.path.abspath(os.path.join(root, file))
                print(input_path)    
                
                # Извлекаем только страницы 2, 5, 7 и применяем сжатие
                success, stats = extract_and_compress_pages(
                    input_path,
                    output_path = input_path.replace('.pdf', '_compressed.pdf'),
                    page_numbers=[],
                    dpi = 100,
                    jpeg_quality = 20,
                )

                if success:
                    print(f"Успешно! Новый файл содержит {stats['processed_pages']} страниц.")
                else:
                    print(f"Ошибка: {stats.get('error')}")