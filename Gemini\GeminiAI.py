"""
АСИНХРОННАЯ ОБРАБОТКА ФАЙЛОВ С КЭШИРОВАНИЕМ

Эта система предоставляет мощные возможности для асинхронной обработки файлов с помощью Google Gemini API
с двойным уровнем кэширования для оптимизации производительности и стоимости.

ПЕРЕМЕННЫЕ ОКРУЖЕНИЯ ДЛЯ НАСТРОЙКИ ЦЕН:
- GEMINI_2_5_FLASH_INPUT_COST - Стоимость входных токенов для gemini-2.5-flash
- GEMINI_2_5_FLASH_OUTPUT_COST - Стоимость выходных токенов для gemini-2.5-flash
- GEMINI_2_5_FLASH_CACHED_COST - Стоимость кэшированных токенов для gemini-2.5-flash
- GEMINI_2_5_PRO_INPUT_COST - Стоимость входных токенов для gemini-2.5-pro
- GEMINI_2_5_PRO_OUTPUT_COST - Стоимость выходных токенов для gemini-2.5-pro
- GEMINI_2_5_PRO_CACHED_COST - Стоимость кэшированных токенов для gemini-2.5-pro
- GEMINI_2_5_FLASH_LITE_INPUT_COST - Стоимость входных токенов для gemini-2.5-flash-lite
- GEMINI_2_5_FLASH_LITE_OUTPUT_COST - Стоимость выходных токенов для gemini-2.5-flash-lite
- GEMINI_2_5_FLASH_LITE_CACHED_COST - Стоимость кэшированных токенов для gemini-2.5-flash-lite
"""

# pip install google-genai

import ast
import asyncio
import base64
import hashlib
import json
import os
import sys
from typing import Union, Dict, Any

import aiofiles
import fitz
from dotenv import load_dotenv
from google import genai
from google.genai import types

# Локальные импорты
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from prompt import PROMPT_EXAMPLE_GEMINI, PROMPT_OCR_CONTROL
from DataBase import (
    create_pool, save_to_pg_cache, get_from_pg_cache,
    cleanup_expired_pg_cache, get_pg_cache_stats
)

load_dotenv()

# =============================================================================
# КОНСТАНТЫ И НАСТРОЙКИ
# =============================================================================

# Модель по умолчанию
MODEL = "gemini-2.5-flash"

# Конфигурация API ключей
API_KEYS = [
    "GEMINI_API_KEY_AHMED",
    "GEMINI_API_KEY_PRESTIGE"
]

# Настройки кэширования
CACHE_TTL_HOURS = 24  # Время жизни кэша в часах
FORCE_CACHE_UPDATE = False  # Принудительное обновление кэша

# Настройки автоматического переключения моделей
MODELS = [
    "gemini-2.5-flash",      # Основная модель (самая мощная)
    "gemini-2.5-flash-lite"  # Резервная модель (выше лимиты)
]

# Стоимость токенов по умолчанию (USD за 1M токенов)
DEFAULT_COST_PRICING = {
    "gemini-2.5-flash": {"input": 0.30, "output": 2.50, "cached": 0.075},
    "gemini-2.5-pro": {"input": 1.25, "output": 10.00, "cached": 0.30},
    "gemini-2.5-flash-lite": {"input": 0.10, "output": 0.40, "cached": 0.0375}
}

# =============================================================================
# ГЛОБАЛЬНЫЕ ПЕРЕМЕННЫЕ (изменяемые во время выполнения)
# =============================================================================

# Управление API ключами
current_api_key_index = 0

# Управление моделями
CURRENT_MODEL_INDEX = 0
CURRENT_MODEL = MODELS[CURRENT_MODEL_INDEX]
RATE_LIMIT_COOLDOWN = 300  # 5 минут cooldown перед возвратом к основной модели
LAST_RATE_LIMIT_TIME = 0   # Время последней ошибки 429

# Глобальные пулы и соединения
_pg_pool = None
_pool_lock = asyncio.Lock()

# Актуальные цены (вычисляются динамически)
COST_PRICING = {}

# =============================================================================
# ИНИЦИАЛИЗАЦИЯ
# =============================================================================

def _initialize_cost_pricing():
    """Инициализирует актуальные цены из переменных окружения."""
    global COST_PRICING
    pricing = {}

    for model in DEFAULT_COST_PRICING.keys():
        model_pricing = {}

        # Input cost
        input_env = f"{model.upper().replace('-', '_')}_INPUT_COST"
        model_pricing['input'] = float(os.getenv(input_env, DEFAULT_COST_PRICING[model]['input']))

        # Output cost
        output_env = f"{model.upper().replace('-', '_')}_OUTPUT_COST"
        model_pricing['output'] = float(os.getenv(output_env, DEFAULT_COST_PRICING[model]['output']))

        # Cached cost
        cached_env = f"{model.upper().replace('-', '_')}_CACHED_COST"
        model_pricing['cached'] = float(os.getenv(cached_env, DEFAULT_COST_PRICING[model]['cached']))

        pricing[model] = model_pricing

    COST_PRICING = pricing

# Инициализация цен
_initialize_cost_pricing()

# Инициализация API ключа
def _initialize_api_key():
    """Инициализирует API ключ."""
    api_key = get_current_api_key()
    if not api_key:
        raise ValueError("Ни один из API ключей не найден в .env файле.")
    return api_key

api_key = _initialize_api_key()


# =============================================================================
# ФУНКЦИИ УПРАВЛЕНИЯ API КЛЮЧАМИ
# =============================================================================

def get_current_api_key():
    """Получает текущий API ключ."""
    global current_api_key_index
    key_name = API_KEYS[current_api_key_index]
    api_key = os.environ.get(key_name)
    if not api_key:
        print(f"API ключ '{key_name}' не найден в .env файле.")
        return None
    return api_key


def switch_to_next_api_key():
    """Переключается на следующий API ключ."""
    global current_api_key_index
    old_index = current_api_key_index
    current_api_key_index = (current_api_key_index + 1) % len(API_KEYS)

    old_key = API_KEYS[old_index]
    new_key = API_KEYS[current_api_key_index]

    print(f"Переключаемся с API ключа '{old_key}' на '{new_key}'")
    return True

# =============================================================================
# ФУНКЦИИ УПРАВЛЕНИЯ МОДЕЛЯМИ
# =============================================================================

def switch_to_fallback_model():
    """Переключается на резервную модель при ошибке 429."""
    global CURRENT_MODEL_INDEX, CURRENT_MODEL, LAST_RATE_LIMIT_TIME
    if CURRENT_MODEL_INDEX == 0:  # Если на основной модели
        CURRENT_MODEL_INDEX = 1  # Переключаемся на резервную
        CURRENT_MODEL = MODELS[CURRENT_MODEL_INDEX]
        # Use time.time() since this function may be called from thread context
        import time
        LAST_RATE_LIMIT_TIME = time.time()
        print(f"Переключаемся на резервную модель: {CURRENT_MODEL}")
        print(f"Лимиты: 15 RPM (вместо 10 RPM)")
        return True
    return False


def try_switch_back_to_primary():
    """Пытается вернуться к основной модели после cooldown периода."""
    global CURRENT_MODEL_INDEX, CURRENT_MODEL, LAST_RATE_LIMIT_TIME
    if CURRENT_MODEL_INDEX == 1:  # Если на резервной модели
        # Use time.time() since this function may be called from thread context
        import time
        current_time = time.time()
        if current_time - LAST_RATE_LIMIT_TIME >= RATE_LIMIT_COOLDOWN:
            CURRENT_MODEL_INDEX = 0  # Возвращаемся к основной
            CURRENT_MODEL = MODELS[CURRENT_MODEL_INDEX]
            print(f"Возвращаемся к основной модели: {CURRENT_MODEL}")
            print(f"Лимиты: 10 RPM (повышенная мощность)")
            return True
    return False


def get_current_model():
    """Получает текущую активную модель."""
    try_switch_back_to_primary()  # Проверяем, можно ли вернуться к основной
    return CURRENT_MODEL


def get_model_limits_info(model: str) -> str:
    """Получает информацию о лимитах модели."""
    limits = {
        "gemini-2.5-flash": "10 RPM / 500 RPD",
        "gemini-2.5-flash-lite": "15 RPM / 500 RPD",
        "gemini-2.5-pro": "5 RPM / 100 RPD"
    }
    return limits.get(model, "Неизвестно")


# =============================================================================
# ФУНКЦИИ УПРАВЛЕНИЯ КЭШЕМ
# =============================================================================

async def get_pg_pool():
    """Получает или создает пул соединений PostgreSQL с потокобезопасностью."""
    global _pg_pool

    # Если пул уже создан, возвращаем его
    if _pg_pool is not None:
        return _pg_pool

    # Используем блокировку для предотвращения одновременного создания пула
    async with _pool_lock:
        # Проверяем еще раз после получения блокировки
        if _pg_pool is None:
            try:
                _pg_pool = await create_pool()
                print("Пул соединений PostgreSQL успешно создан")
            except Exception as e:
                print(f"Ошибка при создании пула соединений: {e}")
                raise
        return _pg_pool


def get_cache_key(file_path: str, prompt: str, model: str) -> str:
    """Генерирует уникальный ключ кэша на основе файла, промпта и модели."""
    try:
        # Получаем размер файла
        file_size = os.path.getsize(file_path)

        # Создаем хэш на основе пути к файлу, размера, промпта и модели
        content = f"{file_path}:{file_size}:{prompt}:{model}"
        return hashlib.md5(content.encode('utf-8')).hexdigest()
    except Exception as e:
        print(f"Ошибка при генерации ключа кэша: {e}")
        return ""


async def get_from_cache(cache_key: str) -> Union[str, None]:
    """Получает результат из кэша PostgreSQL."""
    if not cache_key:
        return None

    try:
        pool = await get_pg_pool()
        cached_data = await get_from_pg_cache(pool, "gemini", cache_key)

        if cached_data and isinstance(cached_data, dict):
            cached_tokens = cached_data.get('cached_tokens', 0)
            if cached_tokens and cached_tokens > 0:
                print(f"Найдено в кэше (системный кэш: {cached_tokens} токенов)")
            else:
                print("Найдено в локальном кэше")
            response_text = cached_data.get('response_text')
            return response_text if response_text is not None else None

        return None

    except Exception as e:
        print(f"Ошибка при чтении из кэша: {e}")
        return None


async def save_to_cache(cache_key: str, response_text: str, tokens_info=None, cached_tokens: int = 0):
    """Сохраняет результат в кэш PostgreSQL."""
    if not cache_key or not response_text:
        return

    try:
        pool = await get_pg_pool()

        # Получаем размер файла из cache_key (если нужно)
        file_size = 0  # Пока не используем

        # Генерируем prompt_hash
        prompt_hash = ""  # Пока не используем

        # Получаем текущую модель для сохранения
        current_model = get_current_model()

        success = await save_to_pg_cache(
            pool, "gemini", cache_key, response_text,
            tokens_info or {}, cached_tokens, current_model, prompt_hash, file_size,
            CACHE_TTL_HOURS
        )

        if success:
            if cached_tokens > 0:
                print(f"Сохранено в кэш (системный кэш: {cached_tokens} токенов)")
            else:
                print("Сохранено в локальный кэш")

    except Exception as e:
        print(f"Ошибка при сохранении в кэш: {e}")


async def cleanup_expired_cache():
    """Удаляет истекшие записи из кэша PostgreSQL."""
    try:
        pool = await get_pg_pool()
        await cleanup_expired_pg_cache(pool, "gemini")
    except Exception as e:
        print(f"Ошибка при очистке кэша: {e}")


async def get_cache_stats() -> dict:
    """Получает статистику кэша PostgreSQL."""
    try:
        pool = await get_pg_pool()
        stats = await get_pg_cache_stats(pool, "gemini")

        if 'gemini' in stats:
            gemini_stats = stats['gemini']
            # Получаем текущую модель для статистики
            current_model = get_current_model()
            return {
                "total_entries": gemini_stats['total_entries'],
                "active_entries": gemini_stats['active_entries'],
                "expired_entries": gemini_stats['expired_entries'],
                "model_stats": {current_model: gemini_stats['active_entries']}
            }

        return {
            "total_entries": 0,
            "active_entries": 0,
            "expired_entries": 0,
            "model_stats": {}
        }

    except Exception as e:
        print(f"Ошибка при получении статистики кэша: {e}")
        return {
            "total_entries": 0,
            "active_entries": 0,
            "expired_entries": 0,
            "model_stats": {}
        }


def calculate_cost(tokens_info: dict, model: str) -> dict:
    """Рассчитывает стоимость запроса."""
    if not tokens_info or model not in COST_PRICING:
        return {"total_cost": 0.0, "input_cost": 0.0, "output_cost": 0.0, "cached_cost": 0.0}

    pricing = COST_PRICING[model]

    input_tokens = tokens_info.get('input_tokens', 0)
    output_tokens = tokens_info.get('output_tokens', 0)
    cached_tokens = tokens_info.get('cached_tokens', 0)

    # Стоимость в USD за миллион токенов
    input_cost = (input_tokens / 1_000_000) * pricing['input']
    output_cost = (output_tokens / 1_000_000) * pricing['output']
    cached_cost = (cached_tokens / 1_000_000) * pricing['cached']

    total_cost = input_cost + output_cost + cached_cost

    return {
        "total_cost": round(total_cost, 6),
        "input_cost": round(input_cost, 6),
        "output_cost": round(output_cost, 6),
        "cached_cost": round(cached_cost, 6),
        "input_tokens": input_tokens,
        "output_tokens": output_tokens,
        "cached_tokens": cached_tokens
    }


# =============================================================================
# ВСПОМОГАТЕЛЬНЫЕ ФУНКЦИИ
# =============================================================================

def encode_pdf(pdf_path: str) -> Union[str, None]:
    """Синхронно кодирует файл в base64."""
    try:
        if not os.path.exists(pdf_path):
            return None
        with open(pdf_path, "rb") as pdf_file:
            return base64.b64encode(pdf_file.read()).decode("utf-8")
    except Exception as e:
        print(f"Ошибка при кодировании файла: {e}")
        return None


def get_pages_count(file_path: str) -> int:
    """Синхронно получает количество страниц в PDF."""
    if file_path.lower().endswith('.pdf'):
        try:
            doc = fitz.open(file_path)
            pages_count = len(doc)
            doc.close()
            return pages_count
        except Exception as e:
            print(f"Ошибка при чтении PDF: {e}")
            return 1
    return 1


def get_file_extension(file_path: str) -> Union[str, None]:
    """Получает расширение файла."""
    if not os.path.exists(file_path):
        print(f"Файл не найден: {file_path}")
        return None
    _, ext = os.path.splitext(file_path)
    return ext.lower().lstrip('.')


def get_mime_type(file_path: str) -> str:
    """Синхронно определяет MIME-тип файла."""
    ext = get_file_extension(file_path)
    if ext == 'pdf':
        return 'application/pdf'
    elif ext in ['png', 'bmp', 'tiff']:
        return f'image/{ext}'
    elif ext in ['jpg', 'jpeg']:
        return 'image/jpeg'
    return 'text/plain'


def smart_parse(obj: str) -> Any:
    """Пытается распарсить строку как JSON или Python литерал."""
    if isinstance(obj, (dict, list)):
        return obj
    try:
        return json.loads(obj)
    except json.JSONDecodeError:
        try:
            return ast.literal_eval(obj)
        except (ValueError, SyntaxError):
            return obj  # Возвращаем как есть, если не удалось распарсить


def clear_text(text: str | None) -> str | None:
    """
    Очищает текст от лишних символов и форматирования, сохраняя числа и буквы.
    Всегда применяется к ответам Gemini AI для стандартизации вывода.
    """
    if not text:
        return text

    text = text.strip()

    # Удаляем очень длинные последовательности одинаковых символов
    for char in ['-', '_', '=', '*', '+', '#', '@', '$', '%', '^', '&']:
        import re
        escaped_char = re.escape(char)
        pattern = escaped_char + '{100,}'  # ищем 100 и более повторений
        text = re.sub(pattern, char * 10, text)  # заменяем на 10 символов

    # Очищаем множественные пробелы
    text = re.sub(r'\s+', ' ', text)

    # Создаем набор разрешенных символов
    allowed_chars = set()
    allowed_chars.update('абвгдеёжзийклмнопрстуфхцчшщъыьэюя')
    allowed_chars.update('АБВГДЕЁЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯ')
    allowed_chars.update('abcdefghijklmnopqrstuvwxyz')
    allowed_chars.update('ABCDEFGHIJKLMNOPQRSTUVWXYZ')
    allowed_chars.update('0123456789')
    allowed_chars.update(' .,;:!?()[]{}')
    allowed_chars.update('+-=*/@#$%^&')
    allowed_chars.update('<>|\\')
    allowed_chars.update('/\'"')

    # Удаляем странные символы, сохраняем буквы, цифры, пробелы и основные знаки
    cleaned_text = ''.join(
        char for char in text
        if char in allowed_chars or ord(char) > 127
    )

    return cleaned_text.strip()


def clear_json_text(json_string: str) -> Any:
    """Очищает ответ модели от markdown и парсит его как JSON."""
    if isinstance(json_string, str):
        try:
            text = json_string.strip()
            if text.startswith('```json'):
                text = text[7:]
            if text.endswith('```'):
                text = text[:-3]
            return smart_parse(text)
        except Exception as e:
            print(f"Ошибка при очистке JSON текста: {e}")
            return json_string
    return json_string


# =============================================================================
# ОСНОВНЫЕ ФУНКЦИИ API
# =============================================================================

def extract_entity_from_page_by_gemini(
    pdf_path: str,
    prompt: str = PROMPT_EXAMPLE_GEMINI
) -> Union[str, None]:
    """Синхронно извлекает данные из файла с помощью Gemini."""
    try:
        if not os.path.exists(pdf_path):
            print(f"Файл не найден: {pdf_path}")
            return None

        with open(pdf_path, "rb") as f:
            file_data = f.read()

        if not file_data:
            print(f"Не удалось прочитать файл: {pdf_path}")
            return None

        mime_type = get_mime_type(pdf_path)
        model = get_current_model()
        print(f"Используем модель: {model} ({get_model_limits_info(model)})")

        client = genai.Client(api_key=get_current_api_key())
        contents = types.Content(
            parts=[
                types.Part.from_text(text=prompt),
                types.Part.from_bytes(mime_type=mime_type, data=file_data),
            ],
        )
        generate_content_config = types.GenerateContentConfig(
            temperature=0.1,
            thinking_config=types.ThinkingConfig(thinking_budget=-1),
        )
        response = client.models.generate_content(
            model=model, contents=contents, config=generate_content_config
        )

        return response.text

    except Exception as e:
        print(f"Ошибка в синхронной функции: {e}")
        return None


async def encode_file_async(file_path: str) -> Union[bytes, None]:
    """Асинхронно читает файл и возвращает его содержимое в виде байтов."""
    try:
        if not os.path.exists(file_path):
            return None
        async with aiofiles.open(file_path, "rb") as f:
            return await f.read()
    except Exception as e:
        print(f"Ошибка при асинхронном чтении файла: {e}")
        return None


async def get_mime_type_async(file_path: str) -> str:
    """Асинхронно определяет MIME-тип файла."""
    try:
        if not os.path.exists(file_path):
            return 'text/plain'
        _, ext = os.path.splitext(file_path)
        ext = ext.lower().lstrip('.')
        if ext == 'pdf':
            return 'application/pdf'
        elif ext in ['png', 'bmp', 'tiff']:
            return f'image/{ext}'
        elif ext in ['jpg', 'jpeg']:
            return 'image/jpeg'
        return 'text/plain'
    except Exception as e:
        print(f"Ошибка при определении MIME-типа: {e}")
        return 'text/plain'


async def extract_entity_from_file_async(
    file_path: str,
    prompt: str = PROMPT_EXAMPLE_GEMINI,
    max_retries: int = 3
) -> Union[str, None]:
    """Асинхронно извлекает данные из файла с помощью Gemini API."""
    try:
        file_data = await encode_file_async(file_path)
        if not file_data:
            print(f"Не удалось прочитать файл: {file_path}")
            return None

        mime_type = await get_mime_type_async(file_path)
        print(f"Выполняется запрос к Gemini API для файла: {os.path.basename(file_path)}...")

        for attempt in range(max_retries):
            try:
                def _sync_request():
                    current_model = get_current_model()
                    current_api_key = get_current_api_key()
                    print(f"Используем модель: {current_model} ({get_model_limits_info(current_model)})")

                    client = genai.Client(api_key=current_api_key)
                    contents = types.Content(
                        parts=[
                            types.Part.from_text(text=prompt),
                            types.Part.from_bytes(mime_type=mime_type, data=file_data),
                        ],
                    )
                    generate_content_config = types.GenerateContentConfig(
                        temperature=0.1,
                        thinking_config=types.ThinkingConfig(thinking_budget=-1),
                    )
                    response = client.models.generate_content(
                        model=current_model, contents=contents, config=generate_content_config
                    )
                    return response.text

                response_text = await asyncio.to_thread(_sync_request)
                return clear_text(response_text)

            except Exception as e:
                error_message = str(e).lower()
                http_status = None

                try:
                    if hasattr(e, 'response'):
                        response_obj = getattr(e, 'response', None)
                        if response_obj and hasattr(response_obj, 'status_code'):
                            http_status = getattr(response_obj, 'status_code', None)
                except (AttributeError, TypeError):
                    pass

                if not http_status:
                    if "429" in error_message:
                        http_status = 429
                    elif "503" in error_message:
                        http_status = 503
                    elif "400" in error_message:
                        http_status = 400
                    elif "401" in error_message:
                        http_status = 401

                if http_status == 429 or "rate limit" in error_message or "quota" in error_message:
                    if attempt < max_retries - 1:
                        if switch_to_next_api_key():
                            print("Переключились на следующий API ключ для повторной попытки")
                            continue
                        if switch_to_fallback_model():
                            print("Переключаемся на резервную модель для повторной попытки")
                            continue
                        wait_time = (2 ** attempt) * 60
                        print(f"HTTP 429: Ждем {wait_time} сек...")
                        await asyncio.sleep(wait_time)
                        continue
                    else:
                        print("HTTP 429: Лимит запросов превышен")
                        return None

                elif http_status == 503 or "unavailable" in error_message or "overloaded" in error_message:
                    if attempt < max_retries - 1:
                        wait_time = (3 ** attempt) + 1
                        print(f"HTTP 503: Ждем {wait_time} сек...")
                        await asyncio.sleep(wait_time)
                        continue
                    else:
                        print("HTTP 503: Сервис недоступен")
                        return None

                elif http_status == 400 or "invalid" in error_message or "bad request" in error_message:
                    print(f"HTTP 400: Ошибка в запросе: {e}")
                    return None

                elif http_status == 401 or "unauthorized" in error_message:
                    print("HTTP 401: Ошибка авторизации")
                    return None

                elif http_status == 500 or "internal server error" in error_message:
                    if attempt < max_retries - 1:
                        wait_time = (2 ** attempt) * 2
                        print(f"HTTP 500: Ждем {wait_time} сек...")
                        await asyncio.sleep(wait_time)
                        continue
                    else:
                        print("HTTP 500: Сервер недоступен")
                        return None

                else:
                    print(f"Неизвестная ошибка: {e}")
                    return None

        return None

    except Exception as e:
        print(f"Критическая ошибка: {e}")
        return None


async def extract_entity_with_cache_async(
    file_path: str,
    prompt: str = PROMPT_EXAMPLE_GEMINI,
    use_cache: bool = True
) -> Union[str, None]:
    """Асинхронно извлекает данные из файла с кэшированием."""
    try:
        if not os.path.exists(file_path):
            print(f"Файл не найден: {file_path}")
            return None

        current_model = get_current_model()
        cache_key = get_cache_key(file_path, prompt, current_model)

        if use_cache and not FORCE_CACHE_UPDATE:
            cached_result = await get_from_cache(cache_key)
            if cached_result:
                return cached_result

        print(f"Обрабатываем файл: {os.path.basename(file_path)}")

        file_data = await encode_file_async(file_path)
        if not file_data:
            print(f"Не удалось прочитать файл: {file_path}")
            return None

        mime_type = await get_mime_type_async(file_path)

        def _sync_request():
            active_model = get_current_model()
            current_api_key = get_current_api_key()
            print(f"Используем модель: {active_model} ({get_model_limits_info(active_model)})")

            client = genai.Client(api_key=current_api_key)
            contents = types.Content(
                role="user",
                parts=[
                    types.Part.from_text(text=prompt),
                    types.Part.from_bytes(mime_type=mime_type, data=file_data),
                ],
            )
            generate_content_config = types.GenerateContentConfig(
                temperature=0.1,
                thinking_config=types.ThinkingConfig(thinking_budget=-1),
            )
            response = client.models.generate_content(
                model=active_model, contents=contents, config=generate_content_config
            )

            print(response.text[:100])
            return response.text

        response_text = await asyncio.to_thread(_sync_request)
        response_text = clear_text(response_text)

        if use_cache and response_text:
            await save_to_cache(cache_key, response_text)

        return response_text

    except Exception as e:
        print(f"Ошибка при обработке файла: {e}")
        return None


async def process_batch_with_cache_async(
    file_paths: list[str],
    prompt: str = PROMPT_EXAMPLE_GEMINI,
    max_concurrent: int = 3,
    use_cache: bool = True
) -> Dict[str, Union[str, None]]:
    """Асинхронно обрабатывает несколько файлов с кэшированием."""
    print(f"Начинаем пакетную обработку {len(file_paths)} файлов...")

    await cleanup_expired_cache()
    cache_stats = await get_cache_stats()
    print(f"Статистика кэша: {cache_stats['active_entries']} активных записей")

    semaphore = asyncio.Semaphore(max_concurrent)
    total_cost = 0.0
    cache_hits = 0
    api_calls = 0

    async def process_single_file(file_path: str):
        nonlocal cache_hits, api_calls, total_cost

        async with semaphore:
            try:
                current_model = get_current_model()
                cache_key = get_cache_key(file_path, prompt, current_model)
                cached_result = await get_from_cache(cache_key) if use_cache else None

                if cached_result and not FORCE_CACHE_UPDATE:
                    cache_hits += 1
                    cost_info = {"total_cost": 0.001, "cached": True}
                    return file_path, cached_result, cost_info

                api_calls += 1
                result = await extract_entity_with_cache_async(file_path, prompt, use_cache)
                cost_info = {"total_cost": 0.01, "cached": False}
                total_cost += cost_info["total_cost"]

                return file_path, result, cost_info

            except Exception as e:
                print(f"Ошибка при обработке файла {os.path.basename(file_path)}: {e}")
                return file_path, None, {"total_cost": 0.0, "cached": False, "error": str(e)}

    tasks = [process_single_file(file_path) for file_path in file_paths]
    results = await asyncio.gather(*tasks, return_exceptions=True)

    result_dict = {}
    successful_count = 0
    failed_count = 0
    total_files = len(file_paths)

    for i, result in enumerate(results):
        file_path = file_paths[i]

        if isinstance(result, Exception):
            print(f"Критическая ошибка: {result}")
            result_dict[file_path] = None
            failed_count += 1
            continue

        if isinstance(result, tuple) and len(result) == 3:
            actual_file_path, file_result, cost_info = result
            result_dict[actual_file_path] = file_result

            if file_result is not None:
                successful_count += 1
            else:
                failed_count += 1
        else:
            print(f"Неожиданный формат результата для файла {os.path.basename(file_path)}")
            result_dict[file_path] = None
            failed_count += 1

    print("
РЕЗУЛЬТАТЫ ОБРАБОТКИ:"    print(f"   Успешно обработано: {successful_count}/{total_files}")
    print(f"   Ошибок: {failed_count}/{total_files}")
    print(f"   Попаданий в кэш: {cache_hits}/{total_files}")
    print(f"   API запросов: {api_calls}/{total_files}")
    print(".4f")
    print(".4f")

    return result_dict


async def process_multiple_files_async(
    file_paths: list[str],
    prompt: str = PROMPT_EXAMPLE_GEMINI,
    max_concurrent: int = 3
) -> Dict[str, Union[str, None]]:
    """Асинхронно обрабатывает несколько файлов параллельно."""
    print(f"Начинаем обработку {len(file_paths)} файлов...")

    semaphore = asyncio.Semaphore(max_concurrent)

    async def process_single_file(file_path: str):
        async with semaphore:
            result = await extract_entity_from_file_async(file_path, prompt)
            return file_path, result

    tasks = [process_single_file(file_path) for file_path in file_paths]
    results = await asyncio.gather(*tasks, return_exceptions=True)

    result_dict = {}
    successful_count = 0
    failed_count = 0

    for i, result in enumerate(results):
        file_path = file_paths[i]

        if isinstance(result, Exception):
            print(f"Критическая ошибка: {result}")
            result_dict[file_path] = None
            failed_count += 1
            continue

        if isinstance(result, tuple) and len(result) == 2:
            actual_file_path, file_result = result
            result_dict[actual_file_path] = file_result

            if file_result is not None:
                successful_count += 1
            else:
                failed_count += 1
        else:
            print(f"Неожиданный формат результата")
            result_dict[file_path] = None
            failed_count += 1

    print(f"Обработка завершена: {successful_count} успешно, {failed_count} неудачно")
    return result_dict


# =============================================================================
# ТЕСТОВЫЕ ФУНКЦИИ
# =============================================================================

async def main_async():
    """Главная функция для демонстрации."""
    if os.name == "nt":
        os.system("cls")
    else:
        os.system("clear")

    test_file = r"C:\Rasim\Python\ScanDocument\Test VN & TTN.pdf"

    if not os.path.exists(test_file):
        print(f"Тестовый файл не найден: {test_file}")
        return

    print("=== АСИНХРОННАЯ ОБРАБОТКА ФАЙЛОВ ===\n")

    print("\n--- 3. Тестирование ПАКЕТНОЙ обработки ---")
    files_to_process = [test_file]
    batch_results = await process_multiple_files_async(files_to_process, PROMPT_OCR_CONTROL)

    print("\n[РЕЗУЛЬТАТЫ ПАКЕТНОЙ ОБРАБОТКИ]:")
    for file_path, result in batch_results.items():
        print(f"\nФайл: {os
