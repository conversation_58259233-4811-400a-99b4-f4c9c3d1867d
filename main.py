# Извлекает каждый документ из PDF в отдельный файл.
# Наименование файла -формату: тип_документа_номер_документа_дата.pdf
# Использует OCR для извлечения текста с страницы PDF
# сохраняет файлы в папку с исходным файлом

import logging
import sys
from datetime import datetime
import re
import time
import fitz  # PyMuPDF
import os
from ProcessingPdf import resize_pdf
from DataBase import main_database_async
import asyncio
from pathlib import Path
from ExtractEmptyFiles import is_blank


from Gemini import gemini_extract_info_from_page
from fixed_gemini_extractor import extract_info_from_page


if os.name == "nt":
    os.system("cls")
else:
    os.system("clear")


# Настраиваем логирование
try:
    # Создаем директорию для логов, если её нет
    log_dir = os.path.join(os.getcwd(), 'logs')
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    log_file = os.path.join(log_dir, "pdf_processing.log")

    # Настраиваем базовое логирование
    logging.basicConfig(
        level=logging.INFO,  # Изменяем уровень с ERROR на INFO
        format='%(asctime)s.%(msecs)03d - %(levelname)s - %(message)s\n%(pathname)s:%(lineno)d\n%(exc_info)s',
        datefmt='%Y-%m-%d %H:%M:%S',
        handlers=[
            logging.FileHandler(log_file, mode='a', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

    # Создаем и настраиваем логгер для этого модуля
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.INFO)

    # Проверяем, что логгер работает
    logger.info(f"Логирование настроено. Файл логов: {log_file}")

    # Устанавливаем уровень INFO для всех логгеров
    for name in logging.root.manager.loggerDict:
        logging.getLogger(name).setLevel(logging.INFO)

except Exception as e:
    print(f"Ошибка при настройке логирования: {str(e)}")
    # Настраиваем минимальное логирование в случае ошибки
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler()]
    )
    logger = logging.getLogger(__name__)


PROMPT = """
У тебя есть текст на украинском языке, полученный после OCR.
В нём могут быть ошибки (например, "0" вместо "О", "3" вместо "З", пробелы, потерянные символы и т.д).
В этом тексте необходимо извлечь следующую информацию:

1. **Имя покупателя** — строка.
2. **Код покупателя** — строго 8 цифр.
3. Для каждого документа покупателя:
   - **Тип документа** (например, "Видаткова накладна, Товарно-транспортна накладна, Акт, Договір, Угода, Замовлення").
   - **Дата документа** в формате ДД.ММ.ГГГГ.
     Если дата указана с названием месяца (например, "01 січня 2025"), приведи її к формату "01.01.2025".
   - **Номер документа** — строка.
4. **Тип страницы** - лицевая, обратная.

Особенности:
- Поставщик всегда называется "Престиж". Код 41098985. Его данные игнорируй.
- Данные покупателя находятся в строках с ключевыми словами: "Вантажоодержувач", "Покупець", "Код покупця".
- Документов может быть несколько — извлекай каждый документ отдельно.
- Детально проанализируй данные, и четко определи к какому документу они относятся.
- Номер "видатковой", "товарно-транспортной" - число. У других типов документов - могут содержать текст.
- Символы в номерах "Мо", "№", "Ne" - не выводи.
- По контексту определи "Тип страницы", первая страница или продолжение.
- Документ Товарно-транспортная накладная содержит ключевые слова - "вантаж", "транспорт", "перевез", "Пункт навантаження".
- Если документ "Товарно-транспортна накладна" (есть ключевые слова: "вантаж", "транспорт", "перевез", "Пункт навантаження") и нет номера документа, тогда:
    -- в тексте много слов "короб". Тебе надо найти последнее слово "короб". Внимательнее будь.
    -- Присвой номер документу "ТТН_ВН_" + номер который находится после "короб", которое ты нашел. Именно "короб", игнорируй другие.
- Будь внимателен. Документы не дублируй.
- Если данные не найдены, верни None.

ВАЖНО: Возвращай ответ в чистом формате JSON, без markdown-форматирования,
без обёртки в тройные обратные кавычки (```), без префикса ```json.
Просто чистый JSON объект:
{
  "buyer": {
    "name": "...",
    "code": "12345678"
  },
  "page_type": "...",
  "documents": [
    {
      "type": "...",
      "date": "ДД.ММ.ГГГГ",
      "number": "..."
    }
  ]
}
ВАЖНО: Перепроверь, чтобы структура, типы данных, названия ключей соответствовала заданию.
"""


# Настраиваем перехват необработанных исключений
def handle_exception(exc_type, exc_value, exc_traceback):
    if issubclass(exc_type, KeyboardInterrupt):
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return
    logging.error(
        "Необработанная ошибка:", exc_info=(exc_type, exc_value, exc_traceback)
    )
    
sys.excepthook = handle_exception
    
def sanitize_filename(name):
    """Удаляет недопустимые символы из имени для создания файла/папки."""
    return re.sub(r'[\\/*?:"<>|]', "_", name)



# Проверка корректности даты
def is_valid_date(date_str):
    from dateutil.parser import parse

    if not date_str or date_str == "null" or date_str == "string":
        return None

    try:
        return parse(date_str, dayfirst=True).date()  # .strftime("%d.%m.%Y")
    except ValueError:
        return None


def get_doc_number_by_table(text: str):
    """
    Ищем номер документа ВН в таблице. Используется в док ТТН
    нужно для случая, если программно не смогли извлечья в НЕМ
    сам номер ВН нужен для определения даты, т.к у ТТН не всегда пишется дата (Эпицентр)
    """

    if  text:
        import re

        text = text.lower()
        text = re.sub(r"\|", " ", text).replace(
            "\n", " "
        )  # Удаляем "|" и переносы строк
        text = re.sub(r"\s{2,}", " ", text)  # Удаляем лишние пробелы

        pattern = r"(видат.*?накл|короб)\D*(\d+)"
        match = re.search(pattern, text)

        if match:
            return match.group(2)  # Группа 2 содержит номер
    return None


async def save_pdf_data_to_single_file(file_name: str, text: str):
    """
    Сохраняет текст в файл с именем, основанным на текущей дате и времени.

    :param text: Текст для сохранения.
    :return: Путь к сохраненному файлу.
    """
    try:
        # Получаем текущую дату и время
        date_parse = datetime.now()
        # Форматируем дату и время в строку
        date_str = date_parse.strftime("%Y%m%d_%H%M%S")
        # Создаем имя файла с использованием текущей даты и времени
        file_name = file_name.replace(".pdf", f" {date_str}.txt")
        file_path = os.path.join(os.getcwd(), file_name)
        # допишем в конец файла
        with open(
            file_path,
            "a",
            encoding="utf-8",
        ) as f:
            f.write(text)
        return file_path
    except Exception as e:
        logging.error(f"Ошибка при сохранении данных в файл {file_name}: {str(e)}", exc_info=True)
        return None


async def complete_data(data_extract: str, ai_data: list[dict]):
    """
    Извлеченные AI данные.В случае, если текущих ]данных мало,
    то AI будет искать в уже отработанных данных информацию и дополнять текущий ответ
    """
    try:
        promt = f"""
            "Старый промт": *****{PROMPT}*****.
            Эту задачу ты уже вополнил. Все результаты сохранены в виде списка словарей.
            Данные:
            #######################
            {ai_data}
            #######################

            Новая задача:
            Теперь тебе надо дополнить текущий ответ, используя уже отработанные данные.
            1) Найди документ с типом "Видаткова накладна" и номером {data_extract}.
            2) Извлеки полностью словарь с ключами как в "Старом промте".
            3) Из документов оставь только информацию о "Видаткова накладна".
            4) Замени тип документа на "Товарно-транспортна накладна".
            5) Замени "page_type" на "извлеченные".
            4) В ответе не должно быть markdown-форматирования, без обёртки в тройные обратные кавычки (```), без префикса ```json.

            ВАЖНО: Извлекаешь только результат который соответствует условию задачи.
            Формат, ключи, структура должны быть как "Старый промт".

            Если данных нет, верни None.
        """
        documents_result = await extract_info_from_page(ai_data, promt)
        return documents_result
    except Exception as e:
        logging.error(f"Ошибка при дополнении данных для {data_extract}: {str(e)}", exc_info=True)
        return None


async def save_problem_pages(file_path, text):
    """
    Сохраняет текст проблемных страниц в файл
    """
    try:
        with open(
            file_path,
            "a",
            encoding="utf-8",
        ) as f:
            f.write(f"{text}\n")
        logging.info(f"Сохранены проблемные данные в файл: {file_path}")
    except Exception as e:
        logging.error(f"Ошибка при сохранении проблемных страниц в файл {file_path}: {str(e)}", exc_info=True)


async def save_data(file_path, text):
    try:
        # информацию о проблемных страницах сохраним в файл
        file_path = file_path.replace(".pdf", "_problem_pages.txt")

        with open(
            file_path,
            "a",
            encoding="utf-8",
        ) as f:
            f.write(f"{text}\n")
        logging.info(f"Сохранены данные в файл: {file_path}")
    except Exception as e:
        logging.error(f"Ошибка при сохранении проблемных страниц в файл {file_path}: {str(e)}", exc_info=True)


async def buyer_info(documents_result: dict):
    try:
        if documents_result.get("buyer", None):
            buyer_name = documents_result.get("buyer", {}).get("name", None)
        else:
            buyer_name = None

        if documents_result.get("buyer", None):
            okpo = documents_result.get("buyer", {}).get("code", None)
        else:
            okpo = None

        return buyer_name, okpo
    except Exception as e:
        logging.error(f"Ошибка при получении информации о клиенте: {str(e)}", exc_info=True)
        return None, None


async def extract_documents(input_path: str, repeate: int =3):
    """
    Извлекает каждый документ из PDF в отдельный файл.

    :param input_path: Путь к входному PDF-файлу.
    :param output_dir: Папка для сохранения выходных файлов.
    """
    try:
        # Проверка на пустые страницы
        threshold = 0.99
        white_level = 250
        date_parse = datetime.now()
        file_name = str(Path(input_path).stem)
        file_path = str(Path(input_path).parent)
        doc_key = f"{file_name}_{date_parse}"

        if not os.path.exists(input_path):
            print(f"Ошибка: Входной файл не найден: {input_path}")
            return

        doc = fitz.open(input_path)
        total_pages = len(doc)
        doc.close()
        doc = fitz.open(input_path)

        # номера корректно обработанных страних.
        # дальнейшем будем сравнивать с занесенными в бд страницами
        # для выявление незанесенных в бд страниц
        correct_page_numbers = []
        uncorrect_page_numbers = []

        # для сохранения оригинального текста с pdf страниц, для записи в файл
        text_raw = input_path

        # Извлеченные AI данные.В случае, если текущих ]данных мало,
        # то AI будет искать в уже отработанных данных информацию и дополнять текущий ответ
        ai_data = []

        for pgno in range(total_pages):
            try:
                print(f"{input_path}\nСтраница {pgno + 1}/{total_pages}", flush=True)

                page = doc.load_page(pgno)
                if await is_blank(page, threshold, white_level):
                    print(f"\nПропуск пустой страницы {pgno + 1}")
                    continue

                text = page.get_text()

                # Сохраняем сырой текст со страницы в отдельный файл
                text_raw += f"\r*** Страница {pgno + 1}/{total_pages}"

                # # Ищем номер ВН в таблице, если не нашли в ответе API
                # doc_number_invoice = get_doc_number_by_table(text)
                # print(f"doc_number_invoice: {doc_number_invoice}")

                # documents_result = await extract_info_from_document(text, PROMT)
                documents_result = await gemini_extract_info_from_page(text, PROMPT)
                if "error" in documents_result.keys():
                    documents_result = await extract_info_from_page(text, PROMPT)

                if "error" in documents_result.keys():
                    error_msg = f"Ошибка в результате AI на странице {pgno + 1}: {documents_result.get('error', '')}"
                    logging.error(error_msg)

                    # Сохраняем ошибку в файл проблемных страниц
                    problem_file_path = input_path.replace(".pdf", "_problem_pages.txt")
                    error_details = f"{input_path}\nСтраница {pgno + 1}/{total_pages}\nОшибка AI: {documents_result.get('error', '')}"
                    await save_problem_pages(problem_file_path, error_details)

                # Проверяем наличие ключа documents
                if (
                    isinstance(documents_result, dict)
                    and "documents" in documents_result.keys() and
                    documents_result["documents"] is not None
                ):
                    documents = documents_result["documents"]
                else:
                    uncorrect_page_numbers.append(pgno)
                    error_msg = f"Отсутствуют данные в результате AI на странице {pgno + 1}"
                    logging.error(error_msg)

                    # Сохраняем ошибку в файл проблемных страниц
                    problem_file_path = input_path.replace(".pdf", "_problem_pages.txt")
                    error_details = f"{input_path}\nСтраница {pgno + 1}/{total_pages}\nОтсутствуют данные в результате AI"
                    await save_problem_pages(problem_file_path, error_details)

                    continue
                    # raise ValueError("Ответ API не корректный")

                ai_data.append(documents_result)


                # если нет данных о клиенте, тип док ТТН, и номер содержит ТТН_ВН_
                if documents_result.get("documents", None) \
                    and  any('ТТН_ВН_' in doc.get('number') for doc in documents_result['documents']):

                    # Безопасное извлечение номера документа с проверками
                    matching_docs = [doc for doc in documents_result['documents'] if doc.get('number') and 'ТТН_ВН_' in doc.get('number')]
                    if matching_docs:
                        doc_number = matching_docs[0].get('number', '')
                        match = re.search(r"\d+", doc_number)
                        docum = match.group(0) if match else None
                    else:
                        docum = None

                    if docum:
                        documents_result = await complete_data(docum, ai_data)

                if documents_result:
                    ai_data.append(documents_result)
                    documents = documents_result["documents"]

                buyer_name, okpo = await buyer_info(documents_result)

                page_type = documents_result.get("page_type", None)
                sort = None
                date_from_1c = None
                data_extract = [
                    tuple(
                        # Заменяем строковые 'None' на Python None в значениях словаря
                        [None if value == "None" else value for value in docum.values()]
                        + [
                            None if value == "None" else value
                            for value in [
                                page_type,
                                pgno,
                                sort,
                                file_name,
                                text,
                                file_path,
                                date_parse,
                                doc_key,
                                date_from_1c,
                                buyer_name,
                                okpo,
                            ]
                        ]
                    )
                    for docum in documents
                ]

                msg = {
                    "doc_key": doc_key,
                    "page_pumber": pgno,  # начинается с 0
                }
                correct_page_numbers.append(msg)

                await main_database_async(data_extract, correct_page_numbers)
                # отправка запросов на api = 15 запросов.мин
                time.sleep(4)

            except Exception as e:
                error_msg = f"Ошибка обработки страницы {pgno + 1}: {str(e)}"
                logging.error(error_msg, exc_info=True)

                # Сохраняем ошибку в файл проблемных страниц
                problem_file_path = input_path.replace(".pdf", "_problem_pages.txt")
                error_details = f"{input_path}\nСтраница {pgno + 1}/{total_pages}\nотсчет с 0\n" \
                                f"{str(e)}\n.Результаты AI:\n{ai_data}\n" \
                                f"*" * 50
                await save_problem_pages(problem_file_path, error_details)

                # Сохраняем в общий файл ошибок
                await save_error_to_file(error_msg, e)

                # raise ValueError(f"\nОшибка обработки страницы {pgno + 1}: {str(e)}")

        doc.close()

    except Exception as e:
        error_msg = f"Критическая ошибка при обработке файла {input_path}: {str(e)}"
        logging.error(error_msg, exc_info=True)

        # Сохраняем ошибку в файл проблемных страниц
        problem_file_path = input_path.replace(".pdf", "_problem_pages.txt")
        try:
            await save_problem_pages(problem_file_path, f"Критическая ошибка: {str(e)}")
        except Exception as save_error:
            logging.error(f"Ошибка при сохранении информации о критической ошибке: {str(save_error)}")

        # Сохраняем в общий файл ошибок
        await save_error_to_file(error_msg, e)

        if "doc" in locals():
            doc.close()

    await save_pdf_data_to_single_file(input_path, text_raw)


def save_document(input_doc, doc_info, page_numbers, output_dir, delete_original=False):
    """
    Сохраняет документ в новый PDF-файл.

    :param input_doc: Входной PDF-документ.
    :param doc_info: Информация о документе.
    :param page_numbers: Список номеров страниц.
    :param output_dir: Папка для сохранения.
    """
    try:
        if doc_info.get("doc_date"):
            filename = f"{doc_info['doc_type']} {doc_info['doc_number']} {doc_info['doc_date']} {page_numbers}.pdf"
        else:
            filename = f"{doc_info['doc_type']} {doc_info['doc_number']} {page_numbers}.pdf"

        filename = sanitize_filename(filename)
        output_path = os.path.join(output_dir, filename)

        output_doc = fitz.open()
        for pgno in page_numbers:
            output_doc.insert_pdf(input_doc, from_page=pgno, to_page=pgno)
        output_doc.save(output_path)
        output_doc.close()

        resize_pdf(output_path, delete_original)
    except Exception as e:
        logging.error(f"Ошибка при сохранении документа {filename}: {str(e)}", exc_info=True)


async def main(path):
    try:
        # Создаем папку Parsed если её нет. В ней будут файлы, которые уже обработаны
        parsed_folder = os.path.join(path, "Parsed")
        if not os.path.exists(parsed_folder):
            os.makedirs(parsed_folder)
            logging.info(f"Создана папка для обработанных файлов: {parsed_folder}")

        # отбирем из папки файлы с расширением pdf и предварительно обработанных OCR программой PDF24
        for file in os.listdir(path):
            if file.endswith("ocred.pdf"):
                input_path = os.path.join(path, file)
                logging.info(f"Начало обработки файла: {input_path}")

                await extract_documents(input_path)

                # пропарсеные файлы перенесем в папку Parsed
                parsed_path = os.path.join(parsed_folder, file)
                os.rename(input_path, parsed_path)
                logging.info(f"Файл обработан и перемещен: {parsed_path}")

    except Exception as e:
        logging.error(f"Ошибка в main: {str(e)}", exc_info=True)


if __name__ == "__main__":
    print("Запуск программы...")
    try:
        # Проверяем, что логирование работает
        logging.info("Начало выполнения программы")

        # input_path = r"C:\Users\<USER>\Desktop\Сверка\Scan\ВЕРЕСЕНЬ 2024"
        input_path = r"d:\Scan\OCR"

        # Проверяем существование директории
        if not os.path.exists(input_path):
            logging.error(f"Директория не существует: {input_path}")
            print(f"Ошибка: директория не существует: {input_path}")
        else:
            asyncio.run(main(input_path))
            logging.info("Обработка успешно завершена")
    except Exception as e:
        logging.error(f"Критическая ошибка при выполнении программы: {str(e)}", exc_info=True)
        print(f"Критическая ошибка: {str(e)}")
    finally:
        print("Обработка завершена")
