# -*- coding: utf-8 -*-
"""
Создает PDF файлы из страниц исходных файлов с использованием GUI.
"""
import fitz  # PyMuPDF
import os
import logging
from typing import List, Tuple, Dict
from pathlib import Path
from dotenv import load_dotenv
import pandas as pd
from sqlalchemy import create_engine, text
from datetime import datetime

# --- Библиотеки для GUI ---
import tkinter as tk
from tkinter import ttk, filedialog, scrolledtext, messagebox
import threading
import queue

# --- Настройка ---
load_dotenv()
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# --- Конфигурация из .env (только для БД) ---
PG_USER = os.getenv("PG_USER")
PG_PASSWORD = os.getenv("PG_PASSWORD")
PG_HOST = os.getenv("PG_HOST_LOCAL")
PG_PORT = os.getenv("PG_PORT")
PG_DBNAME = os.getenv("PG_DBNAME")

# --- Вспомогательная функция для очистки имени папки ---
def sanitize_foldername(name: str) -> str:
    """Удаляет символы, недопустимые в именах папок Windows/Linux."""
    invalid_chars = r'<>:"/\|?*'
    for char in invalid_chars:
        name = name.replace(char, '')
    return name.strip()

# --- Основная логика обработки (без изменений) ---

def is_scan(page: fitz.Page) -> bool:
    if page.get_text().strip():
        return False
    return len(page.get_images()) > 0

def extract_and_compress_pages(
    input_path: str,
    output_path: str,
    page_numbers: List[int],
    dpi: int = 200,
    jpeg_quality: int = 20,
) -> Tuple[bool, Dict]:
    if not os.path.exists(input_path):
        logger.error(f"Входной файл не найден: {input_path}")
        return False, {"error": "Входной файл не существует"}

    doc = None
    new_doc = None
    try:
        doc = fitz.open(input_path)
        new_doc = fitz.open()
        total_pages = len(doc)
        invalid_pages = [p for p in page_numbers if p < 1 or p > total_pages]
        if invalid_pages:
            error_msg = f"Страницы {invalid_pages} не существуют в документе '{os.path.basename(input_path)}' (всего {total_pages})"
            logger.error(error_msg)
            return False, {"error": error_msg}

        for page_num in sorted(page_numbers):
            page = doc.load_page(page_num - 1)
            if is_scan(page):
                pix = page.get_pixmap(dpi=dpi)
                img_bytes = pix.tobytes("jpeg", jpg_quality=jpeg_quality)
                new_page = new_doc.new_page(width=page.rect.width, height=page.rect.height)
                new_page.insert_image(page.rect, stream=img_bytes)
            else:
                new_doc.insert_pdf(doc, from_page=page_num - 1, to_page=page_num - 1)
        
        if len(new_doc) > 0:
            Path(output_path).parent.mkdir(parents=True, exist_ok=True)
            new_doc.save(output_path, garbage=4, deflate=True, clean=True)
            return True, {"processed_pages": len(page_numbers)}
        else:
            return False, {"error": "Нет страниц для обработки"}
    except Exception as e:
        logger.error(f"Ошибка при обработке {input_path}: {str(e)}", exc_info=True)
        return False, {"error": str(e)}
    finally:
        if doc: doc.close()
        if new_doc: new_doc.close()

def fetch_scan_data(buyer_code: str, start_date: str, end_date: str) -> pd.DataFrame:
    """Получает данные из БД с учетом параметров из GUI."""
    conn_string = f"postgresql://{PG_USER}:{PG_PASSWORD}@{PG_HOST}:{PG_PORT}/{PG_DBNAME}"
    try:
        engine = create_engine(conn_string)
        logger.info("Успешное подключение к базе данных.")
        sql = """
            SELECT DISTINCT
                doc_type, doc_date, doc_number, buyer_name, buyer_code,
                page_number, page_type, invoices_numbers, file_name
            FROM t_scan_documents
            WHERE doc_type = 'ТТН'
                AND buyer_code = %(buyer_code)s
                AND doc_date >= %(start_date)s::date
                AND doc_date <= %(end_date)s::date
            ORDER BY
                doc_number, file_name, invoices_numbers, page_type, page_number;
        """
        params = {
            "buyer_code": buyer_code,
            "start_date": start_date,
            "end_date": end_date,
        }
        df = pd.read_sql_query(text(sql), engine, params=params)
        logger.info(f"Загружено {len(df)} записей из t_scan_documents.")
        return df
    except Exception as e:
        logger.error(f"Ошибка подключения к БД: {e}", exc_info=True)
        return pd.DataFrame()

def process_documents_thread(base_input_path_str, base_output_path_str, buyer_code, start_date, end_date):
    """
    Основная функция, которая будет выполняться в отдельном потоке.
    """
    try:
        base_input_path = Path(base_input_path_str)
        base_output_path = Path(base_output_path_str)

        df = fetch_scan_data(buyer_code, start_date, end_date)
        if df.empty:
            logger.warning("Нет данных для обработки по заданным фильтрам. Завершение работы.")
            return

        # Сохранение отчета в Excel
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            excel_filename = f"Scan_Report_{buyer_code}_{timestamp}.xlsx"
            excel_output_path = base_output_path / excel_filename
            df.to_excel(excel_output_path, index=False)
            logger.info(f"Общий отчет сохранен в Excel: {excel_output_path}")
        except Exception as e:
            logger.error(f"Не удалось сохранить общий Excel-отчет. Ошибка: {e}")

        df['doc_date'] = pd.to_datetime(df['doc_date'])
        grouping_keys = ['doc_type', 'doc_date', 'doc_number', 'buyer_name', 'buyer_code']
        grouped = df.groupby(grouping_keys, sort=False)
        logger.info(f"Найдено {len(grouped)} уникальных документов для обработки.")

        for name, group in grouped:
            doc_type, doc_date, doc_number, buyer_name, buyer_code_from_group = name
            
            # --- ИЗМЕНЕНИЕ ЗДЕСЬ ---
            # 1. Очищаем имя клиента от недопустимых символов
            sanitized_buyer_name = sanitize_foldername(buyer_name)
            # 2. Создаем новое имя папки
            client_folder_name = f"{buyer_code_from_group}_{sanitized_buyer_name}"
            # 3. Формируем путь с новой папкой
            output_dir = base_output_path / client_folder_name / str(doc_type)
            # --- КОНЕЦ ИЗМЕНЕНИЯ ---

            formatted_date = doc_date.strftime('%d %m %Y')
            output_filename = f"{doc_type} {doc_number} {formatted_date}.pdf"
            full_output_path = output_dir / output_filename
            source_filename = group['file_name'].iloc[0]
            full_input_path = base_input_path / source_filename
            page_numbers = group['page_number'].astype(int).tolist()

            logger.info(f"--- Обработка: {doc_type} №{doc_number} от {formatted_date} ---")
            success, stats = extract_and_compress_pages(
                input_path=str(full_input_path),
                output_path=str(full_output_path),
                page_numbers=page_numbers,
                dpi=200,
                jpeg_quality=20,
            )
            if success:
                logger.info(f"Документ успешно создан: {stats['processed_pages']} страниц.\n")
            else:
                logger.error(f"Не удалось создать документ. Ошибка: {stats.get('error')}\n")
        
        logger.info("===== ОБРАБОТКА ЗАВЕРШЕНА =====")

    except Exception as e:
        logger.error(f"Критическая ошибка в потоке обработки: {e}", exc_info=True)


# --- Класс для GUI (без изменений) ---

class App:
    def __init__(self, root):
        self.root = root
        self.root.title("Обработчик PDF документов")
        self.root.geometry("800x600")

        self.style = ttk.Style(self.root)
        self.style.theme_use("clam")

        self.source_path = tk.StringVar()
        self.output_path = tk.StringVar()
        self.client_code = tk.StringVar()
        self.start_date = tk.StringVar(value=datetime.now().strftime('%Y-01-01'))
        self.end_date = tk.StringVar(value=datetime.now().strftime('%Y-%m-%d'))

        self.log_queue = queue.Queue()
        self.create_widgets()
        self.setup_logging()
        self.root.after(100, self.process_log_queue)

    def create_widgets(self):
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        form_frame = ttk.LabelFrame(main_frame, text="Параметры", padding="10")
        form_frame.pack(fill=tk.X, expand=False)
        form_frame.columnconfigure(1, weight=1)

        ttk.Label(form_frame, text="Путь к PDF источникам:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(form_frame, textvariable=self.source_path, width=70).grid(row=0, column=1, sticky=tk.EW, padx=5)
        ttk.Button(form_frame, text="Обзор...", command=self.browse_source).grid(row=0, column=2, padx=5)

        ttk.Label(form_frame, text="Путь для сохранения:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(form_frame, textvariable=self.output_path, width=70).grid(row=1, column=1, sticky=tk.EW, padx=5)
        ttk.Button(form_frame, text="Обзор...", command=self.browse_output).grid(row=1, column=2, padx=5)

        ttk.Label(form_frame, text="Код клиента:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(form_frame, textvariable=self.client_code).grid(row=2, column=1, sticky=tk.W, padx=5)
        
        ttk.Label(form_frame, text="Дата C (ГГГГ-ММ-ДД):").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(form_frame, textvariable=self.start_date).grid(row=3, column=1, sticky=tk.W, padx=5)

        ttk.Label(form_frame, text="Дата ПО (ГГГГ-ММ-ДД):").grid(row=4, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(form_frame, textvariable=self.end_date).grid(row=4, column=1, sticky=tk.W, padx=5)

        self.run_button = ttk.Button(main_frame, text="Запустить обработку", command=self.start_processing_thread, style="Accent.TButton")
        self.run_button.pack(pady=10)
        self.style.configure("Accent.TButton", foreground="white", background="green")

        log_frame = ttk.LabelFrame(main_frame, text="Лог выполнения", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True)
        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, state='disabled', bg='black', fg='white')
        self.log_text.pack(fill=tk.BOTH, expand=True)

    def browse_source(self):
        path = filedialog.askdirectory(title="Выберите папку с исходными PDF")
        if path:
            self.source_path.set(path)

    def browse_output(self):
        path = filedialog.askdirectory(title="Выберите папку для сохранения результатов")
        if path:
            self.output_path.set(path)

    def start_processing_thread(self):
        if not self.source_path.get() or not self.output_path.get() or not self.client_code.get():
            messagebox.showerror("Ошибка", "Все поля (пути и код клиента) должны быть заполнены!")
            return
        
        self.run_button.config(state='disabled', text="Обработка...")
        self.log_text.config(state='normal')
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state='disabled')

        thread = threading.Thread(
            target=process_documents_thread,
            args=(
                self.source_path.get(),
                self.output_path.get(),
                self.client_code.get(),
                self.start_date.get(),
                self.end_date.get(),
            ),
            daemon=True
        )
        thread.start()

    def setup_logging(self):
        queue_handler = QueueHandler(self.log_queue)
        queue_handler.setFormatter(logging.Formatter("%(asctime)s - %(levelname)s - %(message)s"))
        logger.addHandler(queue_handler)

    def process_log_queue(self):
        try:
            while True:
                record = self.log_queue.get_nowait()
                self.log_text.config(state='normal')
                self.log_text.insert(tk.END, record + '\n')
                self.log_text.see(tk.END)
                self.log_text.config(state='disabled')
        except queue.Empty:
            pass

        active_threads = [t for t in threading.enumerate() if isinstance(t, threading.Thread) and t.is_alive()]
        processing_thread_is_running = any(t.daemon for t in active_threads)

        if processing_thread_is_running:
             self.run_button.config(state='disabled', text="Обработка...")
        else:
             self.run_button.config(state='normal', text="Запустить обработку")

        self.root.after(100, self.process_log_queue)

class QueueHandler(logging.Handler):
    def __init__(self, log_queue):
        super().__init__()
        self.log_queue = log_queue

    def emit(self, record):
        self.log_queue.put(self.format(record))

if __name__ == "__main__":
    root = tk.Tk()
    app = App(root)
    root.mainloop()
