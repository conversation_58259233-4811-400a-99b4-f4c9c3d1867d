import ast
import locale
import logging
import asyncio
from decimal import Decimal
from collections import OrderedDict
import random

import asyncpg
import pandas as pd
import os

from DeepSeek.DeepSeek_Old import extract_data_by_deepseek
from Gemini.GeminiBase import extract_data_by_gemini_async
from Gemini.GeminiEntity import extract_entity_by_gemini
from Grok.GrokAsync import extract_data_by_grok_async
from Mistral.MistalCorrectText import extract_data_from_text_by_mistral_sync
from Mistral.MistralPdfText import get_data_from_text_by_mistral_async
from OpenRouter.OpenRouter import extract_data_by_deepseek_r1_async
from UTCdatetime import MODEL_DEEPSEEK
from dotenv import load_dotenv
from Gemini.GeminiFlash import extract_data_by_gemini_flash
import psycopg2
import json
from datetime import datetime
from prompt import PROMPT_AMOUNT_WITH_VAT, PROMPT_EXAMPLE_GEMINI
import re
import time

load_dotenv()
DB_USER = os.getenv("PG_USER", "")
DB_PASSWORD = os.getenv("PG_PASSWORD", "")
DB_HOST = os.getenv("PG_HOST_LOCAL", "")
DB_PORT = os.getenv("PG_PORT", "")
DB_NAME = os.getenv("PG_DBNAME", "")

connection_params = {
    'host': DB_HOST,
    'database': DB_NAME,
    'user': DB_USER,
    'password': DB_PASSWORD,
    'port': int(DB_PORT)
}

if not all([DB_USER, DB_PASSWORD, DB_HOST, DB_PORT, DB_NAME]):
    raise ValueError("Не все параметры подключения заданы в .env файле")

logging.basicConfig(
    level=logging.ERROR, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)
token_count_global = []

# КРИТИЧНО: Снижаем лимит параллельных запросов для Grok API
PARALLEL_PROCESSING_LIMIT = 2  # Снижено до 2 для избежания таймаутов
RECORDS_PER_LLM_CALL = 3  # Снижено до 3 записей на вызов для уменьшения размера запроса

# Глобальный счетчик для отслеживания запросов
class RequestTracker:
    def __init__(self):
        self.request_times = []
        self.lock = asyncio.Lock()
    
    async def wait_if_needed(self):
        """Ожидает, если слишком много запросов за последнюю минуту"""
        async with self.lock:
            now = time.time()
            # Удаляем запросы старше минуты
            self.request_times = [t for t in self.request_times if now - t < 60]
            
            # Если приближаемся к лимиту, делаем паузу
            if len(self.request_times) >= 450:  # Оставляем запас от 480
                wait_time = 61 - (now - self.request_times[0])
                if wait_time > 0:
                    print(f"⏳ Приближаемся к rate limit. Пауза {wait_time:.1f} сек...")
                    await asyncio.sleep(wait_time)
            
            # Минимальный интервал между запросами
            if self.request_times:
                min_interval = 0.15  # ~6-7 запросов в секунду максимум
                elapsed = now - self.request_times[-1]
                if elapsed < min_interval:
                    await asyncio.sleep(min_interval - elapsed)
            
            self.request_times.append(time.time())

request_tracker = RequestTracker()

def smart_parse(obj):
    if isinstance(obj, (dict, list)):
        return obj
    elif isinstance(obj, str):
        try:
            return json.loads(obj)
        except json.JSONDecodeError:
            try:
                return ast.literal_eval(obj)
            except (ValueError, SyntaxError):
                raise ValueError("Строка не является валидным JSON или Python-литералом")
    else:
        raise TypeError(f"Неподдерживаемый тип: {type(obj)}")


async def get_db_pool():
    return await asyncpg.create_pool(
        user=DB_USER,
        password=DB_PASSWORD,
        host=DB_HOST,
        port=DB_PORT,
        database=DB_NAME
    )


async def table_exists(pool, table_name):
    try:
        async with pool.acquire() as conn:
            exists = await conn.fetchval(
                """
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = $1
                )
                """,
                table_name
            )
            print(f"Таблица {table_name} существует: {exists}")
            return exists
    except Exception as e:
        logger.error(f"Ошибка при проверке существования таблицы: {e}")
        return False


async def delete_records_from_t_scan_documents(pool):
    """Удаляет записи из t_scan_documents, у которых нет соответствия в t_scan_documents_raw"""
    sql = """
        DELETE FROM t_scan_documents
        WHERE id IN (
            SELECT doc.id
            FROM t_scan_documents AS doc
                LEFT JOIN t_scan_documents_raw AS raw
                    ON raw.id = doc.external_id
            WHERE raw.id IS NULL
        );
    """
    record_count = await run_sql(pool, sql)
    logger.info(f"Удалено записи из t_scan_documents, у которых нет соответствия в t_scan_documents_raw: {record_count}")


async def update_to_unique_buyer_code(pool):
    sql = """
        UPDATE t_scan_documents 
        SET buyer_code = (
            SELECT buyer_code 
            FROM (
                SELECT 
                    buyer_name,
                    buyer_code,
                    COUNT(*) as cnt,
                    ROW_NUMBER() OVER (PARTITION BY buyer_name ORDER BY COUNT(*) DESC, buyer_code) as rn
                FROM t_scan_documents 
                GROUP BY buyer_name, buyer_code
            ) ranked
            WHERE ranked.buyer_name = t_scan_documents.buyer_name 
            AND ranked.rn = 1
        )
        WHERE buyer_name IN (
            SELECT buyer_name 
            FROM t_scan_documents 
            GROUP BY buyer_name 
            HAVING COUNT(DISTINCT buyer_code) > 1
        );
    """
    await run_sql(pool, sql)


async def update_to_unique_buyer_name(pool):
    sql = """
        UPDATE t_scan_documents 
        SET buyer_name = (
            SELECT buyer_name 
            FROM (
                SELECT 
                    buyer_name,
                    buyer_code,
                    COUNT(*) as cnt,
                    ROW_NUMBER() OVER (PARTITION BY buyer_code ORDER BY COUNT(*) DESC, buyer_name) as rn
                FROM t_scan_documents 
                GROUP BY buyer_name, buyer_code
            ) ranked
            WHERE ranked.buyer_code = t_scan_documents.buyer_code 
            AND ranked.rn = 1
        )
        WHERE buyer_code IN (
            SELECT buyer_code 
            FROM t_scan_documents 
            GROUP BY buyer_code 
            HAVING COUNT(DISTINCT buyer_name) > 1
        );
    """
    await run_sql(pool, sql)


async def update_buyer_name_upper(pool):
    sql = """
        UPDATE t_scan_documents AS upd
        SET buyer_name = upper(buyer_name)
        ;
    """
    record_count = await run_sql(pool, sql)
    logger.info(f"Обновлено update_doc_ttn_from_vn: {record_count}")
    return True


async def update_doc_ttn_from_vn(pool):
    sql = """
        UPDATE public.t_scan_documents AS upd
        SET 
            doc_date = COALESCE(upd.doc_date, t1.doc_date),
            doc_number = COALESCE(NULLIF(upd.doc_number, ''), NULLIF(t1.doc_number, '')),
            buyer_name = COALESCE(NULLIF(upd.buyer_name, ''), NULLIF(t1.buyer_name, '')),
            buyer_code = COALESCE(NULLIF(upd.buyer_code, ''), NULLIF(t1.buyer_code, ''))
        FROM public.t_scan_documents AS t1
        WHERE 
            upd.doc_type = 'ТТН' 
            AND upd.page_type = 1
            AND t1.doc_type = 'ВН'
            AND t1.doc_number::text = ANY(
                SELECT jsonb_array_elements_text(upd.invoices_numbers)
            )
            AND upd.file_name = t1.file_name
    ;
    """
    record_count = await run_sql(pool, sql)
    logger.info(f"Обновлено update_doc_ttn_from_vn: {record_count}")
    return True


async def update_doc_vn_from_ttn(pool):
    sql = """
        UPDATE public.t_scan_documents AS upd
        SET 
            doc_date = COALESCE(upd.doc_date, t1.doc_date),
            buyer_name = COALESCE(NULLIF(upd.buyer_name, ''), NULLIF(t1.buyer_name, '')),
            buyer_code = COALESCE(NULLIF(upd.buyer_code, ''), NULLIF(t1.buyer_code, ''))
        FROM public.t_scan_documents AS t1
        WHERE 
            upd.doc_type = 'ВН'
            AND t1.doc_type = 'ТТН' 
            AND t1.page_type = 1
            AND upd.doc_number::text = ANY(
                SELECT jsonb_array_elements_text(t1.invoices_numbers)
            )
            AND upd.file_name = t1.file_name
    ;
    """
    record_count = await run_sql(pool, sql)
    logger.info(f"Обновлено update_doc_ttn_from_vn: {record_count}")
    return True


async def update_doc_ttn_from_ttn(pool):
    sql = """
        UPDATE public.t_scan_documents AS upd
        SET 
            doc_date = COALESCE(upd.doc_date, t1.doc_date),
            doc_number = COALESCE(NULLIF(upd.doc_number, ''), NULLIF(t1.doc_number, '')),
            buyer_name = COALESCE(NULLIF(upd.buyer_name, ''), NULLIF(t1.buyer_name, '')),
            buyer_code = COALESCE(NULLIF(upd.buyer_code, ''), NULLIF(t1.buyer_code, ''))
        FROM public.t_scan_documents AS t1
        WHERE 
            upd.doc_type = 'ТТН' 
            AND upd.page_type IN (3, 999)
            AND t1.doc_type = 'ТТН' 
            AND t1.page_type = 1
            AND upd.invoices_numbers = t1.invoices_numbers
            AND upd.file_name = t1.file_name
            ;
    """
    record_count = await run_sql(pool, sql)
    logger.info(f"Обновлено doc_number, buyer_name, buyer_code у незаполненных ТТН, как правило у последних страниц: {record_count}")
    return True


async def run_sql(pool, sql, params=None):
    try:
        async with pool.acquire() as conn:
            if params is None:
                result_count = await conn.execute(sql)
            else:
                result_count = await conn.execute(sql, (params,))
            return result_count
    except Exception as e:
        logger.error(f"Ошибка при обновлении данных других записей: {e}")
    return 0


def get_description_by_id(id, connection_params=connection_params):
    sql = """
        SELECT description
        FROM t_scan_documents_raw
        WHERE id = %s
    """
    conn = psycopg2.connect(**connection_params)
    try:
        with conn.cursor() as cursor:
            cursor.execute(sql, (id,))
            row = cursor.fetchone()
            return row[0] if row else None
    finally:
        conn.close()


async def create_contents(df_batch: pd.DataFrame):
    if df_batch.empty:
        return "", []

    contents = '*' * 5
    doc_parts = []
    ids = []
    for _, row in df_batch.iterrows():
        id = row['id']
        ids.append(id)
        page_number = row['page_number']
        file_name = row['file_name']
        description = row['description']
        print(f"Обработка документа ID {id}, страница {page_number}, файл {file_name}")
        doc_parts.append(f'{{"id" : {id}, "CONTENT": {json.dumps(description, ensure_ascii=False, indent=2)}}}')
    
    contents += "\n" + "\n".join(doc_parts)
    return contents, ids


def extract_number(text):
    locale.setlocale(locale.LC_ALL, '')
    decimal_separator = locale.localeconv()['decimal_point']
    match = re.search(r"[-+]?\d[\d" + re.escape(decimal_separator) + r"]*", str(text))
    if match:
        number_str = match.group().replace(decimal_separator, ".")
        return float(number_str)
    return None


def add_to_db_sync(result: dict, connection_params=connection_params):
    if not result or not isinstance(result, dict) or not result.get("doc"):
        print("Нет данных для добавления")
        return 0

    docs = []
    for row in result['doc']:
        if 'id' not in row or row['id'] is None:
            continue
        try:
            invoices_numbers = row.get('invoices_numbers', []) if row.get('invoices_numbers', []) is not None else []
            rows_list = row.get('rows_list', []) if row.get('rows_list', []) is not None else []
            processed = {
                'external_id': row['id'],
                'page_type': row.get('page_type'),
                'doc_type': row.get('doc_type'),
                'doc_date': row.get('doc_date'),
                'doc_number': row.get('doc_number'),
                'buyer_name': row.get('buyer_name'),
                'buyer_code': row.get('buyer_code'),
                'supplier_name': row.get('supplier_name'),
                'supplier_code': row.get('supplier_code'),
                'invoices_numbers': sorted(OrderedDict.fromkeys(invoices_numbers)),
                'rows_list': sorted(OrderedDict.fromkeys(rows_list)),
                'thinking_content': row.get('thinking_content'),
                'amount_with_vat': row.get('amount_with_vat')
            }
            docs.append(processed)
        except Exception as e:
            logger.error(f"ERROR. add_to_db_sync: {e}\ndocs: {row}")

    if not docs:
        return 0
        
    conn = None
    try:
        conn = psycopg2.connect(**connection_params)
        cursor = conn.cursor()
        
        values_list = []
        for doc in docs:
            external_id = int(doc['external_id'])
            page_type = str(doc['page_type']) if doc['page_type'] is not None else None
            
            doc_date_obj = None
            if doc['doc_date']:
                try:
                    doc_date_obj = datetime.strptime(str(doc['doc_date']), "%d.%m.%Y").date()
                except (ValueError, TypeError):
                    doc_date_obj = None
            
            buyer_code = str(doc['buyer_code']) if doc['buyer_code'] is not None else None
            doc_number_val_str = str(doc['doc_number']) if doc['doc_number'] is not None else None
            amount_with_vat = 0

            if doc['amount_with_vat'] is None or doc['amount_with_vat'] == 0:
                description = get_description_by_id(external_id)
                amount_with_vat = extract_number(extract_data_from_text_by_mistral_sync(description, PROMPT_AMOUNT_WITH_VAT))

            values = (
                external_id,
                page_type,
                str(doc['doc_type']) if doc['doc_type'] is not None else None,
                doc_date_obj,
                doc_number_val_str,
                str(doc['supplier_name']) if doc['supplier_name'] is not None else None,
                doc['supplier_code'],
                str(doc['buyer_name']) if doc['buyer_name'] is not None else None,
                buyer_code,
                json.dumps(doc.get('invoices_numbers', [])),
                json.dumps(doc.get('rows_list', [])),
                str(doc['thinking_content']) if doc['thinking_content'] is not None else None,
                str(doc['amount_with_vat']) if doc['amount_with_vat'] is not None else amount_with_vat
            )
            values_list.append(values)

        if not values_list:
            return 0

        cursor.executemany("""
            INSERT INTO t_scan_documents (
                external_id, page_type, doc_type, doc_date, doc_number, supplier_name, supplier_code,
                buyer_name, buyer_code, invoices_numbers, rows_list,
                thinking_content, amount_with_vat, created_at
            )
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW())
            ON CONFLICT (external_id) DO UPDATE 
            SET 
                page_type = CASE WHEN EXCLUDED.page_type IS NULL THEN t_scan_documents.page_type ELSE EXCLUDED.page_type END,
                doc_type = CASE WHEN EXCLUDED.doc_type IS NULL THEN t_scan_documents.doc_type ELSE EXCLUDED.doc_type END,
                doc_date = CASE WHEN EXCLUDED.doc_date IS NULL THEN t_scan_documents.doc_date ELSE EXCLUDED.doc_date END,
                doc_number = CASE WHEN EXCLUDED.doc_number IS NULL THEN t_scan_documents.doc_number ELSE EXCLUDED.doc_number END,
                supplier_name = CASE WHEN EXCLUDED.supplier_name IS NULL THEN t_scan_documents.supplier_name ELSE EXCLUDED.supplier_name END,
                supplier_code = CASE WHEN EXCLUDED.supplier_code IS NULL THEN t_scan_documents.supplier_code ELSE EXCLUDED.supplier_code END,
                buyer_name = CASE WHEN EXCLUDED.buyer_name IS NULL THEN t_scan_documents.buyer_name ELSE EXCLUDED.buyer_name END,
                buyer_code = CASE WHEN EXCLUDED.buyer_code IS NULL THEN t_scan_documents.buyer_code ELSE EXCLUDED.buyer_code END,
                invoices_numbers = CASE WHEN EXCLUDED.invoices_numbers::text = '[]' AND t_scan_documents.invoices_numbers::text <> '[]' THEN t_scan_documents.invoices_numbers ELSE EXCLUDED.invoices_numbers END,
                rows_list = CASE WHEN EXCLUDED.rows_list::text = '[]' AND t_scan_documents.rows_list::text <> '[]' THEN t_scan_documents.rows_list ELSE EXCLUDED.rows_list END,
                thinking_content = EXCLUDED.thinking_content,
                amount_with_vat = EXCLUDED.amount_with_vat
        """, values_list)
        
        conn.commit()
        cursor.close()
        
        print(f"Успешно обработано: {len(values_list)} записей")
        return len(values_list)
        
    except Exception as e:
        if conn:
            conn.rollback()
        print(f"Ошибка при добавлении данных: {e}")
        return 0
    finally:
        if conn:
            conn.close()


async def get_all_documents_raw(pool):
    """Получает все записи из t_scan_documents_raw, которые еще не обработаны"""
    await delete_records_from_t_scan_documents(pool)

    try:
        if not await table_exists(pool, 't_scan_documents_raw'):
            logger.error("Таблица t_scan_documents_raw не существует. Обработка невозможна.")
            return pd.DataFrame()

        url_full = """
            SELECT tsd.id, tsd.file_name, tsd.page_number, tsd.description
            FROM t_scan_documents_raw AS tsd
            INNER JOIN
                (
                WITH pages AS (
                    SELECT file_name,
                        max(page_number) AS max_page_number
                    FROM t_scan_documents
                    GROUP BY file_name
                ),
                missing AS (
                    SELECT p.file_name, gs AS page_number
                    FROM pages p
                    CROSS JOIN LATERAL generate_series(1, p.max_page_number) gs
                    WHERE NOT EXISTS (
                        SELECT 1
                        FROM t_scan_documents t
                        WHERE t.file_name = p.file_name
                        AND t.page_number = gs
                    )
                )
                SELECT *
                FROM missing
                ) as missing
            ON tsd.file_name = missing.file_name
                AND tsd.page_number = missing.page_number

            UNION

            SELECT id, file_name, page_number, description
            FROM t_scan_documents_raw
            WHERE id NOT IN (SELECT external_id FROM t_scan_documents WHERE external_id IS NOT NULL)

            ORDER BY file_name, page_number
            ;
        """

        async with pool.acquire() as conn:
            records = await conn.fetch(url_full)
            df = pd.DataFrame(records, columns=['id', 'file_name', 'page_number', 'description'])
            print(f"Получено {len(df)} записей из t_scan_documents_raw")
            logger.info(f"Получено {len(df)} записей из t_scan_documents_raw")
            return df
    except Exception as e:
        logger.error(f"Ошибка при получении документов: {e}")
        return pd.DataFrame()


async def process_one_batch_async(pool, df_batch_for_llm: pd.DataFrame, semaphore: asyncio.Semaphore, repeat=3):
    """Асинхронно обрабатывает один пакет записей с учетом rate limiting"""
    global token_count_global
    
    async with semaphore:
        if df_batch_for_llm.empty:
            return
        
        # Ждем если необходимо для соблюдения rate limit
        await request_tracker.wait_if_needed()
        
        # Добавляем случайную задержку для избежания burst
        await asyncio.sleep(random.uniform(0.2, 0.8))
        
        contents, ids_original = await create_contents(df_batch_for_llm)
        if not contents:
            return

        result_json = None
        attempts = 0
        
        while repeat > 0:
            try:
                attempts += 1
                print(f"Попытка {attempts} для батча с ID {ids_original[0] if ids_original else 'unknown'}")
                
                # Используем Grok с обработкой ошибок
                result = await extract_data_by_grok_async(contents)
                result_json = smart_parse(result)
                
                if isinstance(result_json, dict):
                    break
                    
                repeat -= 1
                if repeat > 0:
                    # Экспоненциальная задержка при неудаче
                    await asyncio.sleep(2 ** (3 - repeat))
                    
            except Exception as e:
                print(f"ERROR in process_one_batch_async: {e}")
                repeat -= 1
                if repeat > 0:
                    # Увеличенная задержка при ошибке
                    await asyncio.sleep(3 + random.uniform(0, 2))

        if result_json and 'doc' in result_json:
            ids_content = [i['id'] for i in result_json['doc']]
            if ids_original == ids_content:
                add_to_db_sync(result_json)
            else:
                print(f"Предупреждение: несоответствие ID. Ожидались {ids_original}, получены {ids_content}")
        else:
            if repeat > 0:
                # Последняя попытка с увеличенной задержкой
                await asyncio.sleep(5)
                await process_one_batch_async(pool, df_batch_for_llm, semaphore, repeat)


async def main():
    global token_count_global
    pool = await get_db_pool()
    
    try:
        df_all_docs = await get_all_documents_raw(pool)

        if df_all_docs.empty:
            logger.info("Нет документов для обработки.")
            return

        # Используем уменьшенный семафор
        semaphore = asyncio.Semaphore(PARALLEL_PROCESSING_LIMIT)

        num_total_records = len(df_all_docs)
        print(f"Начинаем обработку {num_total_records} документов")
        print(f"Батчи по {RECORDS_PER_LLM_CALL} записей, макс. {PARALLEL_PROCESSING_LIMIT} параллельных запросов")

        # Обрабатываем батчами с контролем rate limit
        processed_count = 0
        
        for batch_start in range(0, num_total_records, RECORDS_PER_LLM_CALL * 10):
            batch_end = min(batch_start + RECORDS_PER_LLM_CALL * 10, num_total_records)
            
            tasks = []
            for i in range(batch_start, batch_end, RECORDS_PER_LLM_CALL):
                batch_df = df_all_docs.iloc[i:i + RECORDS_PER_LLM_CALL]
                if not batch_df.empty:
                    task = asyncio.create_task(process_one_batch_async(pool, batch_df, semaphore))
                    tasks.append(task)
            
            if tasks:
                # Обрабатываем группу задач
                await asyncio.gather(*tasks)
                processed_count += len(tasks) * RECORDS_PER_LLM_CALL
                
                print(f"Обработано ~{processed_count}/{num_total_records} записей")
                
                # Пауза между большими группами
                if batch_end < num_total_records:
                    print("Пауза между группами батчей...")
                    await asyncio.sleep(2)

        # Сохраняем токены если используется DeepSeek
        if token_count_global:
            with open("token_count.txt", "a") as f:
                f.write(str(token_count_global))
            print(f"Использовано токенов: {sum(token_count_global) if token_count_global else 0}")

        print("Выполняем постобработку данных...")
        
        # Постобработка данных
        await update_buyer_name_upper(pool)
        await update_doc_ttn_from_vn(pool)
        await update_doc_vn_from_ttn(pool)
        await update_doc_ttn_from_ttn(pool)
        await update_to_unique_buyer_code(pool)
        await update_to_unique_buyer_name(pool)
        
        print("Обработка завершена успешно!")

    except Exception as e:
        logger.error(f"Произошла ошибка в функции main: {e}", exc_info=True)
        print(f"Критическая ошибка: {e}")
    finally:
        if pool:
            await pool.close()


if __name__ == "__main__":
    if os.name == "nt":
        os.system("cls")
    else:
        os.system("clear")
    
    print(f"[{datetime.now()}] Запуск основного процесса...")
    print(f"Параметры: PARALLEL_LIMIT={PARALLEL_PROCESSING_LIMIT}, RECORDS_PER_CALL={RECORDS_PER_LLM_CALL}")
    
    start_time = time.time()
    asyncio.run(main())
    elapsed = time.time() - start_time
    
    print(f"[{datetime.now()}] Основной процесс завершен за {elapsed:.1f} секунд")