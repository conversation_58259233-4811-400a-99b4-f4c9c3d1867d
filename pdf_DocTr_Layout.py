import os
import json
from typing import List, Dict, Any
from doctr.io import DocumentFile
from doctr.models import ocr_predictor
from PIL import Image
import cv2
import numpy as np
from pdf2image import convert_from_path

# --- Настройка ---
DOCUMENT_PATH = r"C:\Rasim\Python\ScanDocument\temp_image\202410 Merge_page_1.png"  # или "your_document.png"
OUTPUT_JSON = "output_ukr.json"
OUTPUT_IMAGE = "output_with_boxes.png"

# Язык для DocTR (украинский)
LANG = "ukr"

# --- Функция для извлечения текста с DocTR (с указанием языка) ---
def extract_text_with_doctr(document_path: str, lang: str = LANG) -> Dict[str, Any]:
    """
    Извлекает текст и структуру документа с помощью DocTR для украинского языка.
    """
    # Загрузка модели DocTR с указанием языка
    model = ocr_predictor(
        det_arch="db_resnet50",
        reco_arch="crnn_vgg16_bn",
        pretrained=True,
        assume_straight_pages=True,
    )

    # Обработка документа
    if document_path.endswith(".pdf"):
        doc = DocumentFile.from_pdf(document_path)
    else:
        doc = DocumentFile.from_images(document_path)

    # Указание языка для распознавания (важно для украинского!)
    result = model(doc)

    # Сохранение результата в JSON
    result.export(OUTPUT_JSON)
    print(f"Результаты сохранены в {OUTPUT_JSON}")

    return result

# --- Визуализация блоков ---
def visualize_blocks(document_path: str, output_image: str = OUTPUT_IMAGE) -> None:
    """
    Визуализирует обнаруженные блоки текста на изображении.
    """
    if document_path.endswith(".pdf"):
        images = convert_from_path(document_path)
        img = np.array(images[0])  # Берём первую страницу PDF
    else:
        img = cv2.imread(document_path)
        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

    # Загрузка результатов DocTR
    with open(OUTPUT_JSON, "r") as f:
        data = json.load(f)

    # Рисуем блоки на изображении
    for page in data["pages"]:
        for block in page["blocks"]:
            # Координаты блока (x1, y1, x2, y2, ...)
            points = np.array(block["geometry"], np.int32).reshape((-1, 1, 2))
            cv2.polylines(img, [points], isClosed=True, color=(0, 255, 0), thickness=2)
            # Подпись блока (первые слова)
            if block["lines"]:
                text = block["lines"][0]["words"][0]["value"]
                cv2.putText(img, text, (int(points[0][0][0]), int(points[0][0][1])),
                            cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)

    # Сохраняем результат
    cv2.imwrite(output_image, cv2.cvtColor(img, cv2.COLOR_RGB2BGR))
    print(f"Визуализация сохранена в {output_image}")

# --- Основной скрипт ---
if __name__ == "__main__":
    print("=== Запуск DocTR для украинского языка (CPU) ===")

    # Шаг 1: Извлечение текста с DocTR (с указанием языка)
    result = extract_text_with_doctr(DOCUMENT_PATH, lang=LANG)

    # Шаг 2: Визуализация блоков
    visualize_blocks(DOCUMENT_PATH)

    print("\nГотово! Проверьте output_ukr.json и output_with_boxes.png.")
