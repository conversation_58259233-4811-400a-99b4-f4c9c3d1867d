"""
Скрипт для проверки соответствия количества записей в базе данных
и количества страниц в PDF файлах.

Алгоритм работы:
1. Подключается к PostgreSQL базе данных
2. Получает все уникальные имена файлов из таблицы t_scan_documents_raw
3. Для каждого файла подсчитывает количество записей в БД
4. Строит путь к PDF файлу: PATH_TO_PDF + file_name
5. Подсчитывает количество страниц в PDF
6. Сравнивает количества и выводит только файлы с несоответствиями
"""

import os
import psycopg2
from dotenv import load_dotenv
import fitz  # PyMuPDF for PDF page count

# Загружаем переменные окружения из .env файла
load_dotenv()

# Параметры подключения к базе данных PostgreSQL
# Используются переменные окружения или значения по умолчанию
connection_params = {
    'host': os.getenv("PG_HOST_LOCAL", "*************"),  # Хост базы данных
    'database': os.getenv("PG_DBNAME", "prestige"),       # Имя базы данных
    'user': os.getenv("PG_USER", "postgres"),             # Пользователь БД
    'password': os.getenv("PG_PASSWORD", "hfvpfc15"),     # Пароль пользователя
    'port': int(os.getenv("PG_PORT", "5432"))             # Порт PostgreSQL
}

# Путь к папке с PDF файлами
# Измените этот путь в соответствии с вашим расположением файлов
PATH_TO_PDF = r"C:\Scan\All\AlreadyAddToDb"

def get_pdf_page_count(pdf_path):
    """
    Получает количество страниц в PDF файле.

    Args:
        pdf_path (str): Полный путь к PDF файлу

    Returns:
        int or None: Количество страниц в PDF, или None если файл не найден/ошибка чтения
    """
    try:
        # Открываем PDF файл с помощью PyMuPDF (fitz)
        doc = fitz.open(pdf_path)
        # Получаем количество страниц
        page_count = len(doc)
        # Закрываем документ для освобождения ресурсов
        doc.close()
        return page_count
    except Exception as e:
        # В случае ошибки выводим сообщение и возвращаем None
        print(f"Error reading PDF {pdf_path}: {e}")
        return None

def main():
    """
    Основная функция скрипта - проверяет соответствие записей в БД и страниц в PDF.

    Логика работы:
    1. Подключается к базе данных
    2. Получает список всех уникальных файлов из t_scan_documents_raw
    3. Для каждого файла проверяет соответствие количества записей и страниц
    4. Собирает список файлов с несоответствиями
    5. Выводит результаты
    """
    try:
        print("Connecting to database...")
        # Устанавливаем соединение с PostgreSQL
        conn = psycopg2.connect(**connection_params)
        cursor = conn.cursor()
        print("Connected successfully")

        # Получаем все уникальные имена файлов из таблицы t_scan_documents_raw
        cursor.execute("SELECT DISTINCT file_name FROM t_scan_documents_raw")
        file_names = cursor.fetchall()

        print(f"Found {len(file_names)} unique files in t_scan_documents_raw")

        # Список для хранения файлов с несоответствиями
        mismatch_files = []

        # Проходим по каждому файлу
        for (file_name,) in file_names:
            # Подсчитываем количество записей для этого файла в базе данных
            cursor.execute("SELECT COUNT(*) FROM t_scan_documents_raw WHERE file_name = %s", (file_name,))
            db_record_count = cursor.fetchone()[0]

            # Формируем полный путь к PDF файлу
            pdf_path = os.path.join(PATH_TO_PDF, file_name)

            # Получаем количество страниц в PDF
            pdf_page_count = get_pdf_page_count(pdf_path)

            # Проверяем, удалось ли прочитать PDF
            if pdf_page_count is not None:
                # Если PDF прочитан, проверяем соответствие количества
                if db_record_count != pdf_page_count:
                    # Добавляем файл в список несоответствий
                    mismatch_files.append({
                        'file_name': file_name,
                        'db_records': db_record_count,
                        'pdf_pages': pdf_page_count,
                        'type': 'mismatch'  # Несоответствие количества
                    })
            else:
                # Если PDF не найден или ошибка чтения
                mismatch_files.append({
                    'file_name': file_name,
                    'db_records': db_record_count,
                    'pdf_pages': None,
                    'type': 'not_found'  # Файл не найден
                })

        # Вывод результатов - только файлы с проблемами
        if mismatch_files:
            print(f"Found {len(mismatch_files)} files with issues:")
            print("=" * 60)

            for file_info in mismatch_files:
                print(f"File: {file_info['file_name']}")
                print(f"  DB records: {file_info['db_records']}")

                if file_info['type'] == 'mismatch':
                    print(f"  PDF pages: {file_info['pdf_pages']}")
                    print("  Status: Mismatch")
                else:
                    print("  PDF: Not found or error")
                print()
        else:
            print("All files match - no issues found!")

        # Закрываем соединение с базой данных
        cursor.close()
        conn.close()

    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
